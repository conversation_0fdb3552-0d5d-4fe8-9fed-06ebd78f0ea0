@extends('theme.layout.master')
@push('css')
@endpush
@section('content')
    <section class="main-dashboard">
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-12">
                    <div class="white_box table_box">
                        <div class="table_header">
                            <div class="user_engagement">
                                <h5>Cruds > Shaft > Material</h5>
                            </div>
                            <div class="side_fields custom_entries">
                               <div class="custom_flex">
                                <div class="custom_search_box">
                                    <form>
                                        <div class="txt_field">
                                            <i class="fa-solid fa-magnifying-glass"></i>
                                            <input type="search" placeholder="Search" class="form-control searchinput">
                                        </div>
                                    </form>
                                </div>
                                <!-- Filter Dropdown -->
                                <!-- Create Button -->
                                <a href="javascript:void(0)" data-bs-toggle="modal" data-bs-target="#create_material" class="btn dark_green_btn"><i class="fa-solid fa-square-plus"></i>Create</a>
                            </div>
                        </div>
                            <div class="status_buttons">
                                <button id="activateSelected" class="btn btn-success">  <i class="fas fa-check-circle"></i> </button>
                                <button id="deactivateSelected" class="btn btn-danger">     <i class="fas fa-ban"></i> </button>
                            </div>
                        </div>
                        <div class="table-responsive">
                            <table class="shaft-type-datatable table table-row-bordered gy-5 custom_sizing checkbox_table" id="shaftTypeTable">
                                <thead>
                                <tr>
                                    <th>
                                        <input type="checkbox" class="select_all_checkboxes">
                                    </th>
                                    <th>SR#</th>
                                    <th>Name</th>
                                    <th>Type</th>
                                    <th>Price</th>
                                    <th>Wood</th>
                                    <th>Iron</th>
                                    <th>Status</th>
                                    <th>Action</th>
                                </tr>
                                </thead>
                                <tbody>

                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Create Modal -->
    <div class="modal fade custom_modal" id="create_material" tabindex="-1" aria-labelledby="createModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title" id="createModalLabel">Create Material</h4>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"><i class="fa-solid fa-close"></i></button>
                </div>
                <div class="modal-body">
                    <form id="models_form" action="{{ url('shf-type') }}" method="POST" enctype="multipart/form-data">
                        @csrf
                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label>Name *</label>
                                    <input type="text" class="form-control" placeholder="Type Here" name="ShfTypeDsc" id="ShfTypeDsc" required>
                                    @error('ShfTypeDsc')
                                    <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label>Type *</label>
                                    <select name="ShfGrpId" id="ShfGrpId" class="form-control">
                                        <option selected disabled>Select Type</option>
                                        @foreach($shfGrps as $shfGrp)
                                            <option value="{{$shfGrp->ShfGrpId??''}}">{{$shfGrp->ShfGrpDsc??''}}</option>
                                        @endforeach
                                    </select>
                                    @error('ShfGrpId')
                                    <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label>Price</label>
                                    <input type="text" class="form-control" id="price" name="price" placeholder="Type Here" oninput="formatPrice(this)">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="custom_checked">
                                    <div class="form-group">
                                        <label>Wood</label>
                                        <input type="checkbox" name="WoodInd" checked value="1" id="WoodInd">
                                        @error('WoodInd')
                                        <div class="text-danger">{{ $message }}</div>
                                        @enderror
                                    </div>
                                    <div class="form-group">
                                        <label>Iron</label>
                                        <input type="checkbox" name="IronInd" checked value="1" id="IronInd">
                                        @error('IronInd')
                                        <div class="text-danger">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="submit_form_btn">
                                    <button type="submit" class="btn light_green_btn">Create</button>
                                    <button type="button" class="btn cancel_btn" data-bs-dismiss="modal">Cancel</button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit Modal -->
    <div class="modal fade custom_modal" id="update_model" tabindex="-1" aria-labelledby="createModalLabel"
         aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title" id="createModalLabel">Edit Model</h4>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"><i
                            class="fa-solid fa-close"></i></button>
                </div>
                <div class="modal-body">
                    <form id="models_form_update" action="{{ route('shf-type.update', 0) }}" method="POST"
                          enctype="multipart/form-data">
                        @csrf
                        @method('PUT')
                        <input type="hidden" name="model_id_update" id="model_id_update">
                        <div class="row">

                            <div class="col-md-12">
                                <div class="form-group">
                                    <label>Name *</label>
                                    <input type="text" class="form-control" placeholder="Type Here" name="ShfTypeDsc" id="ShfTypeDsc_update" required>
                                    @error('ShfTypeDsc')
                                    <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label>Type *</label>
                                    <select name="ShfGrpId" id="ShfGrpId_update" class="form-control">
                                        @foreach($shfGrps as $shfGrp)
                                            <option value="{{$shfGrp->ShfGrpId??''}}">{{$shfGrp->ShfGrpDsc??''}}</option>
                                        @endforeach
                                    </select>
                                    @error('ShfGrpId')
                                    <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label>Price</label>
                                    <input type="text" class="form-control" id="price_update" name="price" placeholder="Type Here" oninput="formatPrice(this)">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="custom_checked">
                                    <div class="form-group">
                                        <label>Wood</label>
                                        <input type="checkbox" name="WoodInd" checked value="1" id="WoodInd_update">
                                        @error('WoodInd')
                                        <div class="text-danger">{{ $message }}</div>
                                        @enderror
                                    </div>
                                    <div class="form-group">
                                        <label>Iron</label>
                                        <input type="checkbox" name="IronInd" checked value="1" id="IronInd_update">
                                        @error('IronInd')
                                        <div class="text-danger">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-12">
                                <div class="submit_form_btn">
                                    <button type="submit" class="btn light_green_btn">Update</button>
                                    <button type="button" class="btn cancel_btn" data-bs-dismiss="modal">Cancel</button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

@endsection
@push('js')

    <script>

        $('#activateSelected, #deactivateSelected').on('click', function () {
            let status = $(this).attr('id') === 'activateSelected' ? 1 : 0;
            let selectedIds = [];

            $('.category-checkbox:checked').each(function () {
                selectedIds.push($(this).val());
            });
            console.log(selectedIds , status);

            if (selectedIds.length > 0) {
                $.ajax({
                    url: '{{ route('toggle.status') }}',
                    method: 'POST',
                    data: {
                        ids: selectedIds,
                        status: status,
                        primary_key: 'ShfTypeCd',
                        model: 'ShfType',
                        _token: '{{ csrf_token() }}',
                    },
                    success: function (response) {
                        if (response.success) {
                            Swal.fire({
                                icon: 'success',
                                title: 'Status Updated!',
                                text: 'The selected categories have been successfully updated.',
                                confirmButtonText: 'OK'
                            }).then(function() {
                                location.reload();
                            });
                        }
                    },
                    error: function () {
                        Swal.fire({
                            icon: 'error',
                            title: 'Oops...',
                            text: 'An error occurred while updating the status. Please try again later.',
                            confirmButtonText: 'OK'
                        });
                    }
                });
            } else {
                Swal.fire({
                    icon: 'warning',
                    title: 'Nothing Selected',
                    text: 'Please select at least one category to activate/deactivate.',
                    confirmButtonText: 'OK'
                });
            }
        });


        $(document).on('click', '.change-type-status', function () {
            var modelId = $(this).attr('model_id');
            var status = $(this).attr('model_status');
            var current_parent = $(this).parent();
            Swal.fire({
                title: 'Are you sure?',
                text: 'You are about to change the status.',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: 'Yes, change it!'
            }).then((result) => {
                if (result.isConfirmed) {
                    var url = "{{ url('shf-type') }}/" + modelId + "/status";
                    $.ajax({
                        type: "POST",
                        url: url,
                        data: {
                            _token: "{{ csrf_token() }}",
                            status: status
                        },
                        success: function (response) {
                            if (response.success) {
                                current_parent.html(response.resultHtml);
                                swal.fire({
                                    icon: 'success',
                                    title: 'Success',
                                    text: response.message,
                                    timer: 4000,
                                });
                            } else {
                                swal.fire({
                                    icon: 'error',
                                    title: 'Error',
                                    text: response.message,
                                    timer: 4000,

                                });
                            }
                        },
                        error: function (xhr, status, error) {
                            swal.fire({
                                icon: 'error',
                                title: 'Error',
                                text: 'An error occurred while changing the status.',
                                timer: 4000,
                            });
                        }
                    });
                }
            });
        });

        $(document).on('click', '.edit_model_button', function () {
            const modelId = $(this).attr('model_id');
            const modelShfTypeDsc = $(this).attr('model_ShfTypeDsc');
            const modelIronInd = $(this).attr('model_IronInd');
            const modelWoodInd = $(this).attr('model_WoodInd');
            const modelShfGrpId = $(this).attr('model_ShfGrpId');
            const modelPrice = $(this).attr('model_Price');

            $('#model_id_update').val(modelId);
            $('#ShfTypeDsc_update').val(modelShfTypeDsc);
            $('#price_update').val(modelPrice);
            $('#ShfGrpId_update').find('option[value="' + modelShfGrpId + '"]').prop('selected', true);

            if (modelIronInd == '1') {
                $('#IronInd_update').prop('checked', true);
            } else {
                $('#IronInd_update').prop('checked', false);
            }

            if (modelWoodInd == '1') {
                $('#WoodInd_update').prop('checked', true);
            } else {
                $('#WoodInd_update').prop('checked', false);
            }

            $('#update_model').modal('show');
        });

        $(document).on("click", ".close-btn", function () {
            var parentDiv = $(this).parent();
            parentDiv.remove();

            if ($(".code_value span").length === 0) {
                $(".code_value").hide();
            }

        });


        $(document).ready(function() {
            $('[data-bs-toggle="tooltip"]').tooltip();
        });
    </script>

    <script>
        $(document).ready(function () {
            var htmlContent = '<div class="dt-layout-cell dt-layout-start"><div class="dt-length"><label for="dt-length-0">Show</label><select aria-controls="DataTables_Table_0" class="dt-input" id="dt-length-0"><option value="10">10</option><option value="25">25</option><option value="50">50</option><option value="-1">All</option></select><label for="dt-length-0">entries</label></div></div>';
            // Append the dropdown to the side fields container
            document.querySelector('.side_fields.custom_entries').insertAdjacentHTML('afterbegin', htmlContent);

            var table = $('.shaft-type-datatable').DataTable({
                processing: true,
                serverSide: true,
                ajax: {
                    url: '{{ route('shf-type.index') }}',
                    type: 'GET',
                    data: function (d) {
                        d.parent = $('#parent').val();
                    },
                    dataSrc: 'data'
                },
                columns: [
                    { data: 'checkbox', name: 'checkbox' },
                    {data: null, name: null, defaultContent: ''},
                    {data: 'name', name: 'name'},
                    {data: 'type', name: 'type'},
                    {data: 'price', name: 'price'},
                    {data: 'wood', name: 'wood'},
                    {data: 'iron', name: 'iron'},
                    {data: 'status', name: 'status'},
                    {data: 'action', name: 'action', orderable: false, searchable: false}
                ],
                order: [[1, 'desc']],
                pageLength: 10,
                lengthChange: false,
                ordering: false,
                searching: true,
                createdRow: function (row, data, dataIndex) {
                    $(row).find('td:eq(1)').html(dataIndex + 1);
                },
                initComplete: function (settings, json) {
                    $('[data-bs-toggle="tooltip"]').tooltip();
                }
            });

            // Change the page length based on dropdown selection
            document.querySelector('#dt-length-0').addEventListener('change', function() {
                var selectedValue = this.value;
                if (selectedValue == "-1") {
                    table.page.len(-1).draw(); // Show all records
                } else {
                    table.page.len(parseInt(selectedValue)).draw(); // Set page length to selected value
                }
            });
            $(document).on("input", '.searchinput', function () {
                var searchValue = $(this).val();
                table.search(searchValue).draw();
            });

            // Select All functionality
            $('.select_all_checkboxes').on('click', function () {
                var checkboxes = $('.category-checkbox');
                var isChecked = $(this).prop('checked'); // Get the state of select_all_checkboxes
                checkboxes.prop('checked', isChecked); // Set all category-checkbox to the same state
            });

            // Update select_all_checkboxes when any category-checkbox is changed
            $(document).on('change', '.category-checkbox', function () {
                var checkboxes = $('.category-checkbox');
                var allChecked = checkboxes.filter(':checked').length === checkboxes.length;
                $('.select_all_checkboxes').prop('checked', allChecked); // Update select_all_checkboxes
            });
        });
    </script>
@endpush
