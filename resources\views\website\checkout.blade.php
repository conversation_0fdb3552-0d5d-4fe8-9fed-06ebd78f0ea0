@extends('website.layout.master')
@section('content')
    <section class="banner">
        <div class="container">
            <div class="row">
                <div class="col-md-12">
                    <div class="main_banner">
                        <h1>Checkout</h1>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <section class="checkout_section">
        <div class="container">
            <div class="row">
                <div class="col-md-12">
                    <div class="shopping_box">
                        <h6>Contact Info</h6><h6>Address</h6><h6>Payment</h6>
                    </div>
                </div>
                <div class="col-md-8 col-sm-7">
                    <form class="stepper_form">
                        <div class="form_steps">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="first_name">First Name</label>
                                        <input id="first_name" type="text" name="first_name" placeholder="First Name" class="form-control" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="last_name">Last Name</label>
                                        <input id="last_name" type="text" name="last_name" placeholder="Last Name" class="form-control" required>
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label for="email">Email</label>
                                        <input id="email" type="email" name="email" placeholder="Email" class="form-control" required>
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label for="phone_number">Phone Number</label>
                                        <input id="phone_number" type="tel" name="phone_number" placeholder="Phone Number" class="form-control" required autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="form_steps">
                            <div class="address_box">
                                <div class="address_content_box">
                                    <div class="address_content">
                                        <label class="custom-radio">
                                            <input type="radio" name="address" checked>
                                            <span class="custom-radio-mark"></span>
                                        </label>
                                        <div class="title_box">
                                            <h6>Mr. Marcus Schuppe</h6>
                                            <p>1131 Dusty Townline, Jacksonville, TX 40322</p>
                                            <div class="contact">
                                                <p>Contact - </p>
                                                <span>(936) 361-0310</span>
                                            </div>
                                        </div>
                                        <div class="address_box_tag">
                                            <span>HOME</span>
                                        </div>
                                    </div>
                                    <div class="buttons_edit">
                                        <div class="edit_forms">
                                            <button type="button" class="edit-btn">Edit</button>
                                            <button type="button" class="remove-btn">Remove</button>
                                        </div>
                                    </div>
                                </div>
                                <div class="address_content_box">
                                    <div class="address_content">
                                        <label class="custom-radio">
                                            <input type="radio" name="address" >
                                            <span class="custom-radio"></span>
                                        </label>
                                        <div class="title_box">
                                            <h6>Office Name Here</h6>
                                            <p>1131 Dusty Townline, Jacksonville, TX 40322</p>
                                            <div class="contact">
                                                <p>Contact- </p>
                                                <span>(936) 361-0310</span>
                                            </div>
                                        </div>
                                        <div class="address_box_tag">
                                            <span>OFFICE</span>
                                        </div>
                                    </div>
                                    <div class="buttons_edit">
                                        <div class="edit_forms">
                                        <button type="button" class="edit-btn">Edit</button>
                                        <button type="button" class="remove-btn">Remove</button>
                                    </div>
                                </div>
                            </div>
                            </div>
                            <div class="new_address">
                                <button type="button" class="add_address">
                                    <i class="fa fa-plus"></i> Add New Address
                                </button>
                                <div class="shipping-address-form shippment_form">
                                    <div class="row">
                                        <div class="col-md-12">
                                            <h6>New Shipping Address</h6>
                                        </div>
                                        <div class="col-md-12">
                                            <div class="checked_state">
                                                <label class="custom-checkbox">
                                                    <div class="checked_state_box">
                                                        <input type="radio" name="address_type">
                                                        <span class="square-checkmark"></span></div>
                                                    <img class="img-fluid" src="{{asset('website')}}/assets/images/Home Icons.png" alt="">
                                                    <span class="heading">Home</span>
                                                </label>
                                                <label class="custom-checkbox">
                                                    <div class="checked_state_box">
                                                        <input type="radio" name="address_type">
                                                        <span class="square-checkmark"></span></div>
                                                    <img class="img-fluid" src="{{asset('website')}}/assets/images/Office Icons.png" alt="">
                                                    <span class="heading">Office</span>
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-md-12">
                                            <div class="form-group">
                                                <label for="address">Address</label>
                                                <input id="address" type="text" placeholder="Add Address" class="form-control" >
                                            </div>
                                        </div>
                                        <div class="col-md-12">
                                            <div class="form-group">
                                                <label for="streetaddress">Street Address</label>
                                                <input id="streetaddress" type="text" placeholder="Select Address" class="form-control" >
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="street_address">Street Address</label>
                                                <input id="street_address" type="text" name="street_address" placeholder="Enter Street Address" class="form-control" >
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="city">City</label>
                                                <input id="city" type="text" name="city" placeholder="City" class="form-control" >
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="state">State</label>
                                                <input id="state" type="text" name="state" placeholder="State" class="form-control" >
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="zip_code">Zip Code</label>
                                                <input id="zip_code" type="text" name="zip_code" placeholder="Zip Code" class="form-control" >
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="form_steps">
                            <div class="address_box payment">
                                <div class="address_payment_box">
                                    <label class="custom-card-radio">
                                    <div class="address_payment">
                                            <input type="radio" name="payment_method" checked>
                                            <span class="custom-radio-mark"></span>
                                        <div class="payment_info">
                                            <img class="img-fluid" src="{{asset('website')}}/assets/images/visa.png" alt="Visa">
                                            <p>•••• 6754</p>
                                            <p class="payment_date">Expires 06/2025</p>
                                        </div>
                                    </div>
                                    </label>
                                    <div class="buttons_edit">
                                        <div class="edit_forms">
                                            <button type="button" class="remove-card-btn">Remove</button>
                                        </div>
                                    </div>

                                </div>
                                <div class="address_payment_box">
                                    <label class="custom-card-radio">
                                    <div class="address_payment">
                                            <input type="radio" name="payment_method">
                                            <span class="custom-radio-mark"></span>

                                        <div class="payment_info">
                                            <img class="img-fluid" src="{{asset('website')}}/assets/images/mastercard.svg" alt="">
                                            <p>•••• 9821</p>
                                            <p class="payment_date">Expires 09/2026</p>
                                        </div>
                                    </div>
                                    </label>
                                    <div class="buttons_edit">
                                        <div class="edit_forms">
                                            <button type="button" class="remove-card-btn">Remove</button>
                                        </div>
                                    </div>
                                </div>
                                <div class="address_payment_box">
                                    <label class="custom-card-radio">
                                    <div class="address_payment">
                                            <input type="radio" name="payment_method">
                                            <span class="custom-radio-mark"></span>
                                        <div class="payment_info">
                                            <p>Cash on Delivery</p>
                                        </div>
                                    </div>
                                    </label>
                                    <div class="buttons_edit">
                                        <div class="edit_forms">
                                            <button type="button" class="remove-card-btn">Remove</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="stepper_button">
                            <div class="light_btn">
                                <button type="button" class="btn btn-primary btn-primary_green" id="prevBtn" disabled="">
                                    <i class="fas fa-arrow-left"></i>
                                </button>
                            </div>
                            <div class="light_btn">
                                <button type="button" class="btn btn-primary btn-primary_green" id="nextBtn">
                                    Next
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="col-md-4 col-sm-5">
                    <div class="summary">
                        <div class="summary_box">
                            <div class="detail_content">
                                <h6>Order Summary</h6>
                                <div class="detail_value sub_total">
                                    <p>Sub Total</p>
                                    <p>${{ isset($coupon) ? number_format($coupon['original_total'], 2) : number_format($order->TtlAmt ?? 0, 2) }}</p>
                                </div>
                                <div class="detail_value discount">
                                    <p>Discount</p>
                                    <p>{{ isset($coupon) ? $coupon['discount_percentage'] : 0 }}%</p>
                                </div>
                                <div class="detail_value shipping">
                                    <p>Shipping</p>
                                    <p class="free">Free</p>
                                </div>
                                <div class="detail_value coupon_applied">
                                    <p>{{ isset($coupon) ? $coupon['type'] . ' Applied' : 'Coupon Applied' }}</p>
                                    <p>${{ isset($coupon) ? number_format($coupon['discount_amount'], 2) : '0.00' }}</p>
                                </div>
                            </div>
                            <div class="total_content">
                                <div class="total_value">
                                    <p>TOTAL</p>
                                    <p>${{ isset($coupon) ? number_format($coupon['new_total'], 2) : number_format($order->TtlAmt ?? 0, 2) }}</p>
                                </div>
                                <div class="coupon_field">
                                    <input type="text" placeholder="Coupon Code" name="coupon_code" id="coupon_code" value="{{ isset($coupon) ? $coupon['code'] : '' }}" readonly>
                                    <i class="fa-regular fa-tag"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
@endsection
@push('js')
    <script>
        document.addEventListener("DOMContentLoaded", function () {
            const steps = document.querySelectorAll(".form_steps");
            const headings = document.querySelectorAll(".shopping_box h6");
            const nextBtn = document.getElementById("nextBtn");
            const prevBtn = document.getElementById("prevBtn");
            let currentStep = 0;

            function showStep(step) {
                steps.forEach((formStep, index) => {
                    formStep.classList.toggle("active", index === step);
                    formStep.style.display = index === step ? "block" : "none";
                });

                headings.forEach((heading, index) => {
                    heading.classList.toggle("active", index === step);
                });

                prevBtn.disabled = step === 0;
                nextBtn.textContent = step === steps.length - 1 ? "Place Order" : "Next";
            }

            function validateStep() {
                const inputs = steps[currentStep].querySelectorAll("[required]");
                let isValid = true;

                inputs.forEach((input) => {
                    if (!input.value.trim()) {
                        input.classList.add("is-invalid");
                        isValid = false;
                    } else {
                        input.classList.remove("is-invalid");
                    }
                });

                return isValid;
            }

            nextBtn.addEventListener("click", () => {
                if (currentStep < steps.length - 1) {
                    if (validateStep()) {
                        currentStep++;
                        showStep(currentStep);
                    }
                } else {
                    if (validateStep()) {
                        document.querySelector(".stepper_form").submit();
                    }
                }
            });

            prevBtn.addEventListener("click", () => {
                if (currentStep > 0) {
                    currentStep--;
                    showStep(currentStep);
                }
            });

            showStep(currentStep);
        });


        $(document).ready(function(){
            $(".add_address, .edit-btn").click(function(){
                $(".shippment_form").show();
                $(".add_address").hide();
            });

            $(".remove-btn").click(function(){
                $(this).closest(".address_content_box").remove();
            });
            $(".remove-card-btn").click(function(){
                $(this).closest(".address_payment_box").remove();
            });
        });

        function loadPaymentMethods() {
            var accountId = `{{Auth::user()->fitter->AcctId??''}}`;
            $.ajax({
                url: '{{ route('accounts.payment-methods.get') }}',
                method: 'POST',
                data: {
                    account_id: accountId,
                    _token: '{{ csrf_token() }}'
                },
                success: function(response) {

                },
                error: function(xhr, status, error) {

                }
            });
        }

    </script>
@endpush

