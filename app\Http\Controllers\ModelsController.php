<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;

use App\Models\ClbType;
use App\Models\HeadDsgn;
use App\Models\Hossel;
use DB;
use Illuminate\Http\Request;
use Yajra\DataTables\Facades\DataTables;
use Illuminate\Validation\Rule;

class ModelsController extends Controller
{
    public function index(Request $request)
    {
        if ($request->ajax()) {
            $models = HeadDsgn::with(['category'])
//                ->whereNotIn('ClbTypeCd', ['W', 'I'])
                ->latest();

            if ($request->has('status') && $request->status != '') {
                $models->where('status', $request->status == 'active' ? '1' : '0');
            }

            if ($request->has('parent') && $request->parent != '') {
                $models->whereHas('category', function ($query) use ($request) {
                    $query->where('ClbTypeDsc', 'like', "%" . $request->parent . "%");
                });
            }

            $models = $models->get();

            return DataTables::of($models)
                ->addColumn('checkbox', function ($row) {
                    return '<input type="checkbox" class="category-checkbox" value="' . $row->HeadDsgnId . '">';
                })
                ->addIndexColumn()
                ->addColumn('image', function ($row) {
                    $imageSrc = asset('website/' . $row->image());
                    $imageTooltip = htmlspecialchars('<img src="' . $imageSrc . '" class="img-fluid" />', ENT_QUOTES, 'UTF-8');

                    return '<img class="img-fluid preview-image" src="' . $imageSrc . '"
                         style="width: 45px;height: 45px;object-fit: cover;border-radius: 50%;"
                        data-bs-toggle="tooltip"
                        data-bs-placement="top"
                        data-bs-html="true"
                        title="' . $imageTooltip . '" />';
                })
                ->addColumn('code', function ($row) {
                    return $row->HeadDsgnCd ?? 'N/A';
                })
                ->addColumn('model', function ($row) {
                    return $row->HeadDsgnDsc ?? 'N/A';
                })
                ->addColumn('type', function ($row) {
                    return $row->type ?? 'N/A';
                })
                ->addColumn('b2b_price', function ($row) {
                    return $row->b2b_price ?? '0';
                })
                ->addColumn('b2c_price', function ($row) {
                    return $row->b2c_price ?? '0';
                })
                ->addColumn('side', function ($row) {
                    return $row->hnd_side ?? 'Both';
                })
                ->addColumn('category', function ($row) {
                    return $row->category->ClbTypeDsc ?? 'N/A';
                })
                ->addColumn('color', function ($row) {
                    $colors = $row->colors() ?? [];
                    $colorHtml = '';
                    foreach ($colors as $color) {
                        $colorHtml .= '<span class="color-box" style="background-color:' . $color . '; width: 20px; height: 20px;"></span>';
                    }
                    return $colorHtml ?: 'N/A';
                })
                ->addColumn('status', function ($row) {
                    return $row->statusHtml ?? 'N/A';
                })
                ->addColumn('action', function ($row) {
                    return '
                    <div class="dropdown">
                        <button class="btn dropdown-toggle" type="button" id="dropdownMenuButton11" data-bs-toggle="dropdown" aria-expanded="false">
                            <img class="ellipsis_img" src="' . asset('website') . '/assets/images/ellipsis.svg">
                        </button>
                        <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton11">
                            <li>
                                <a class="dropdown-item edit_model_button" href="javascript:void(0)"
                                   model_id="' . $row->HeadDsgnId . '"
                                   model_name="' . $row->HeadDsgnDsc . '"
                                   model_image="' . htmlspecialchars(json_encode($row->images()), ENT_QUOTES, 'UTF-8') . '"
                                   model_colors="' . htmlspecialchars(json_encode($row->colors()), ENT_QUOTES, 'UTF-8') . '"
                                   model_club_numbers="' . htmlspecialchars(json_encode($row->club_numbers()), ENT_QUOTES, 'UTF-8') . '"
                                   model_has_loft="' . $row->has_loft . '"  model_has_lie="' . $row->has_lie . '"  model_has_faceangle="' . $row->has_faceangle . '"  model_has_hossel="' . $row->has_hossel . '"
                                   model_category_id="' . $row->ClbTypeCd . '"
                                   model_side="' . $row->hnd_side . '"
                                   model_type="' . $row->type . '"
                                   model_status="' . $row->status . '"
                                   model_b2b_price="' . $row->b2b_price . '"
                                   model_b2c_price="' . $row->b2c_price . '"
                                   >Edit
                                </a>
                            </li>
                            <li>
                                <form action="' . route('models.destroy', $row->HeadDsgnId) . '" method="POST" class="delete-form">
                                    ' . csrf_field() . method_field('DELETE') . '
                                    <a class="dropdown-item" href="javascript:void(0)" onclick="showDeleteConfirmation(this)">Delete</a>
                                </form>
                            </li>
                        </ul>
                    </div>';
                })
                ->rawColumns(['image', 'color', 'status', 'action','checkbox'])
                ->make(true);
        }

        $categories = ClbType::latest()->get();

        return view('dashboard.Cruds.Models.index', compact('categories'));
    }

    public function create()
    {
        return view('categories.create');
    }

    public function store(Request $request)
    {
        extract($request->all());

//        $request->validate([
//            'category_id' => 'required',
//            'HeadDsgnDsc' => 'required|unique:headdsgn,HeadDsgnDsc,NULL,id,ClbTypeCd,' . $request->category_id,
//            'images' => 'required|array|min:1',
//            'hnd_side' => 'required',
//            'type' => 'required',
//            'b2b_price' => [
//                Rule::requiredIf(function () use ($request) {
//                    return in_array($request->type, ['b2b', 'both']);
//                }),
//                'nullable',
//                'numeric',
//                'max:1000000',
//            ],
//            'b2c_price' => [
//                Rule::requiredIf(function () use ($request) {
//                    return in_array($request->type, ['b2c', 'both']);
//                }),
//                'nullable',
//                'numeric',
//                'max:1000000',
//            ],
////            'colors' => 'required|array|min:1',
////            'images.*' => 'image|mimes:jpeg,png,jpg,gif|max:2048',
//        ], [
//            'category_id.required' => 'Please select a category.',
//            'HeadDsgnDsc.required' => 'Model name is required.',
//            'HeadDsgnDsc.unique' => 'Model name already taken for the selected parent category.',
//            'images.required' => 'Please upload at least one image.',
//            'hnd_side.required' => 'Please select a Side',
//            'type.required' => 'Please select a Type',
//            'b2b_price.required' => 'B2B Price is required.',
//            'b2b_price.numeric' => 'B2B Price must be a valid number.',
//            'b2b_price.max' => 'B2B Price cannot be more than 1,000,000.',
//            'b2c_price.required' => 'B2C Price is required.',
//            'b2c_price.numeric' => 'B2C Price must be a valid number.',
//            'b2c_price.max' => 'B2C Price cannot be more than 1,000,000.',
////            'price.required' => 'Price is required.',
////            'price.numeric' => 'Price must be a valid number.',
////            'price.max' => 'Price cannot be more than 100,000.',
////            'colors.required' => 'Please add at least one color.',
////            'images.array' => 'The images field must be an array.',
////            'images.*.image' => 'Each file must be an image.',
////            'images.*.mimes' => 'Only PNG, JPEG, JPG, and GIF images are allowed.',
////            'images.*.max' => 'The image size must be less than 2MB.',
//        ]);

        try {
            DB::beginTransaction();
            $model = HeadDsgn::create([
                'ClbTypeCd' => $category_id,
                'HeadDsgnCd' => strtoupper(implode('', array_map(function ($word) {
                    return strtoupper($word[0]);
                }, explode(' ', $HeadDsgnDsc ? $HeadDsgnDsc : '')))),
                'HeadDsgnDsc' => $HeadDsgnDsc,
                'images' => json_encode($images ?? ['models/default.png']),
                'colors' => json_encode($colors ?? ['#000000']),
                'has_loft' => isset($has_loft) ? $has_loft : 0,
                'has_lie' => isset($has_lie) ? $has_lie : 0,
                'has_faceangle' => isset($has_faceangle) ? $has_faceangle : 0,
                'has_hossel' => isset($has_hossel) ? $has_hossel : 0,
                'club_numbers' => isset($club_numbers) ? json_encode($club_numbers) : null,
                'hnd_side' => $hnd_side,
                'type' => $type,
                'b2b_price' => isset($b2b_price) ? $b2b_price : null,
                'b2c_price' => isset($b2c_price) ? $b2c_price : null,
            ]);
            DB::commit();
            if ($model) {
                return redirect(url('models'))->with(['title' => 'Done', 'message' => 'model created successfully.', 'type' => 'success']);
            } else {
                return redirect(url('models'))->with(['title' => 'Fail', 'message' => 'Unable to create model.', 'type' => 'error']);
            }

        } catch (\Exception $e) {
            DB::rollback();
            return $e->getMessage();
        }

    }

    public function show($id)
    {
        return redirect(url('categories'));
    }

    public function edit($id)
    {
    }

    public function update(Request $request, $id)
    {
        $model = HeadDsgn::where('HeadDsgnId', $request->model_id_update)->firstOrFail();

        $request->validate([
            'category_id_update' => 'required',
            'HeadDsgnDsc_update' => 'required|unique:headdsgn,HeadDsgnDsc,' . $model->HeadDsgnId . ',HeadDsgnId,ClbTypeCd,' . $request->category_id_update,
            'update_hnd_side' => 'required',
            'type_update' => 'required',
            'b2b_price_update' => [
                Rule::requiredIf(function () use ($request) {
                    return in_array($request->type, ['b2b', 'both']);
                }),
                'nullable',
                'numeric',
                'max:1000000',
            ],
            'b2c_price_update' => [
                Rule::requiredIf(function () use ($request) {
                    return in_array($request->type, ['b2c', 'both']);
                }),
                'nullable',
                'numeric',
                'max:1000000',
            ],
//            'images_update' => 'required|array|min:1',
//            'colors' => 'required|array|min:1',
        ], [
            'category_id_update.required' => 'Category is required.',
            'HeadDsgnDsc_update.required' => 'Model name is required.',
            'HeadDsgnDsc_update.unique' => 'Model name already exists under the selected category.',
            'update_hnd_side.required' => 'Please select a Side',
            'type_update.required' => 'Please select a Type',
            'b2b_price_update.required' => 'B2B Price is required.',
            'b2b_price_update.numeric' => 'B2B Price must be a valid number.',
            'b2b_price_update.max' => 'B2B Price cannot be more than 1,000,000.',
            'b2c_price_update.required' => 'B2C Price is required.',
            'b2c_price_update.numeric' => 'B2C Price must be a valid number.',
            'b2c_price_update.max' => 'B2C Price cannot be more than 1,000,000.',
//            'images_update.required' => 'Please upload at least one image.',
//            'colors.required' => 'Please add at least one color.',
        ]);

        try {
            DB::beginTransaction();

            if ($request->has('removed_images') && count($request->removed_images) > 0) {
                foreach ($request->removed_images as $removedImg) {
                    $this->deleteImage($removedImg);
                }
            }

            $model->update([
                'ClbTypeCd' => $request->category_id_update,
                'HeadDsgnDsc' => $request->HeadDsgnDsc_update,
                'images' => isset($request->images_update) ? json_encode($request->images_update) : null,
                'colors' => json_encode($request->colors ?? ['#000000']),
                'status' => $request->status,
                'has_loft' => isset($request->has_loft) ? $request->has_loft : 0,
                'has_lie' => isset($request->has_lie) ? $request->has_lie : 0,
                'has_faceangle' => isset($request->has_faceangle) ? $request->has_faceangle : 0,
                'has_hossel' => isset($request->has_hossel) ? $request->has_hossel : 0,
                'club_numbers' => isset($request->club_no) && $request->club_no == 1
                    ? (isset($request->club_numbers) ? json_encode($request->club_numbers) : ($model->club_numbers ?? null))
                    : null,
                'hnd_side' => $request->update_hnd_side,
                'type' => $request->type_update,
                'b2b_price' => isset($request->b2b_price_update) ? $request->b2b_price_update : null,
                'b2c_price' => isset($request->b2c_price_update) ? $request->b2c_price_update : null,
            ]);

            DB::commit();

            return redirect(url('models'))->with([
                'title' => 'Done',
                'message' => 'Model updated successfully.',
                'type' => 'success',
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            return redirect(url('models'))->with([
                'title' => 'Fail',
                'message' => 'Update failed: ' . $e->getMessage(),
                'type' => 'error',
            ]);

        }
    }

    public function destroy($id)
    {
        try {
            HeadDsgn::where('HeadDsgnId', $id)->update(['deleted_at' => now()]);
            return redirect(url('models'))->with(['title' => 'Done', 'message' => 'Model deleted successfully.', 'type' => 'success']);

        } catch (\Exception $e) {
            return redirect(url('models'))->with(['title' => 'Fail', 'message' => 'Unable to delete model.', 'type' => 'error']);

        }
    }

    public function updateStatus(Request $request, $id)
    {
        if ($request->method() == 'GET') {
            return response()->json([
                'success' => false,
                'message' => 'Invalid request method.',
            ], 400);
        }

        $model = HeadDsgn::where('HeadDsgnId', $id)->firstOrFail();

        $request->validate([
            'status' => 'required|in:0,1',
        ], [
            'status.required' => 'Please select a status.',
            'status.in' => 'Status must be either Active (1) or Inactive (0).',
        ]);

        try {
            DB::beginTransaction();

            // Update status
            $model->status = $request->status;
            $model->save();

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Model status updated successfully.',
                'status' => $model->status,
                'resultHtml' => ($model->status == 1) ? '<span style="cursor:pointer;" class="success change-model-status"  model_id="' . $model->HeadDsgnId . '" model_status="0" >Active</span>' : '<span style="cursor:pointer;" class="danger change-model-status"  model_id="' . $model->HeadDsgnId . '" model_status="1" >Inactive</span>'
            ], 200);
        } catch (\Exception $e) {
            DB::rollback();
            return response()->json([
                'success' => false,
                'message' => 'Unable to update model status, please try again.',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    public function uploadModelImages(Request $request)
    {
        try {
            $request->validate([
                'image' => 'required|min:1',
                'image.*' => 'image|mimes:jpeg,png,jpg,gif|max:2048',
            ], [
                'image.required' => 'Please upload at least one image.',
                'image.*.image' => 'Each file must be an image.',
                'image.*.mimes' => 'Only PNG, JPEG, JPG, and GIF images are allowed.',
                'image.*.max' => 'The image size must be less than 2MB.',
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Image uploaded successfully.',
                'image' => $this->storeImage('models', $request->file('image')),
            ], 200);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Unable to upload image, please try again.',
                'error' => $e->getMessage(),
            ], 500);

        }

    }

    public function deleteModelImage(Request $request)
    {
        try {
            $request->validate([
                'filename' => 'required|min:1',
            ], [
                'filename.required' => 'Please upload at least one image.',
            ]);

            $this->deleteImage($request->filename);

            return response()->json([
                'success' => true,
                'message' => 'Image delete successfully.',
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Unable to delete image, please try again.',
                'error' => $e->getMessage(),
            ], 500);
        }

    }

    public function getModels(Request $request, $category_id = null)
    {
        try {
            $side = $request->query('side');
            $query = HeadDsgn::where('ClbTypeCd', $category_id)->where('status','1')->whereIn('type',['b2b','Both']);
            if (isset($side)) {
                $query->where(function($query) use ($side) {
                    $query->where('hnd_side', $side)
                        ->orWhere('hnd_side', 'Both');
                });
            }
            $models = $query->get(['HeadDsgnId', 'HeadDsgnDsc']);
            return response()->json($models);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Unable to fetch models, please try again.',
                'error' => $e->getMessage(),
            ], 500);
        }
    }



    public function getColors($model_id = null)
    {
        try {
            $model = HeadDsgn::where('HeadDsgnId', $model_id)->with('hossels')->firstOrFail();
            $colors = json_decode($model->colors, true) ?? [];
            $colorNames = config('color.names', []);
            $colorOptions = array_map(function ($color) use ($colorNames) {
                return [
                    'id' => $color,
                    'name' => $colorNames[$color] ?? $color
                ];
            }, $colors);
            return response()->json([
                'color_options' => $colorOptions,
                'model' => $model
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Unable to fetch colors, please try again.',
                'error' => $e->getMessage(),
            ], 500);
        }
    }


}
