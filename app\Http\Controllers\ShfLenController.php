<?php

namespace App\Http\Controllers;

use App\Models\FaceAngle;
use App\Models\ShfLen;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Yajra\DataTables\Facades\DataTables;

class ShfLenController extends Controller
{

    public function index(Request $request)
    {
        if ($request->ajax()) {
            $shfLen = ShfLen::latest()->get();
            return DataTables::of($shfLen)
                ->addColumn('checkbox', function ($row) {
                    return '<input type="checkbox" class="category-checkbox" value="' . $row->ShfLenId . '">';
                })
                ->addIndexColumn()
                ->addColumn('price', function ($row) {
                    return $row->price ?? '0';
                })
                ->addColumn('status', function ($row) {
                    return $row->statusHtml ?? 'N/A';
                })
                ->addColumn('action', function ($row) {
                    return '
                    <div class="dropdown">
                        <button class="btn dropdown-toggle" type="button" id="dropdownMenuButton11" data-bs-toggle="dropdown" aria-expanded="false">
                            <img class="ellipsis_img" src="' . asset('website') . '/assets/images/ellipsis.svg">
                        </button>
                        <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton11">
                            <li><a class="dropdown-item edit_model_button" href="javascript:void(0)"
                                       model_id="' . $row->ShfLenId . '"
                                       model_Price="' . $row->price . '"
                                       model_ShfLenDsc=" '. htmlspecialchars($row->ShfLenDsc, ENT_QUOTES) . '"
                                       model_ShfLenDiff="' . $row->ShfLenDiff . '">Edit</a></li>
                                <li>
                                <form action="' . route('shflen.destroy', $row->ShfLenId) . '" method="POST" class="delete-form">
                                    ' . csrf_field() . method_field('DELETE') . '
                                    <a class="dropdown-item" href="javascript:void(0)" onclick="showDeleteConfirmation(this)">Delete</a>
                                </form>
                            </li>
                        </ul>
                    </div>';
                })
                ->rawColumns(['status', 'action','checkbox'])
                ->make(true);
        }

        return view('dashboard.Cruds.Lengths.index');
    }

    public function create()
    {
        //
    }

    public function store(Request $request)
    {
        extract($request->all());
        $request->validate([
            'ShfLenDsc' => 'required',
        ], [
            'ShfLenDsc.required' => 'ShfLen name is required.',
            'ShfLenDsc.unique' => 'ShfLen name already exist.',
        ]);

        try {
            DB::beginTransaction();
            preg_match('/[a-zA-Z]+/', $ShfLenDsc, $matches);
            $ShfLenCd = strtoupper($matches[0][0]) . number_format(abs((float)$request->ShfLenDiff), 1, '.', '');

            ShfLen::create([
                'ShfLenCd' => $ShfLenCd ?? null,
                'ShfLenDsc' => $ShfLenDsc ?? null,
                'ShfLenDiff' => $ShfLenDiff ?? null,
                'price' =>  isset($request->price)  ? $request->price : null,
            ]);

            DB::commit();

            return redirect()->route('shflen.index')->with(['title' => 'Done', 'message' => 'ShfLen created successfully.', 'type' => 'success']);

        } catch (\Exception $e) {
            DB::rollback();
            dd($e->getMessage());
            return redirect()->route('shflen.index')->with(['title' => 'Fail', 'message' => 'Unable to create ShfLen.', 'type' => 'error']);

        }

    }

    public function show($id)
    {

    }

    public function edit($id)
    {

    }

    public function update(Request $request, $id)
    {
        $loft = ShfLen::where('ShfLenId', $request->model_id_update)->firstOrFail();

        $request->validate([
            'ShfLenDsc_update' => 'required',
        ], [
            'ShfLenDsc_update.required' => 'ShfLen name iss required.',
            'ShfLenDsc_update.unique' => 'ShfLen name already exists.',
        ]);

        try {
            DB::beginTransaction();
            preg_match('/[a-zA-Z]+/', $request->ShfLenDsc_update, $matches);

            $ShfLenCd = strtoupper($matches[0][0]) . number_format(abs((float)$request->ShfLenDiff_update), 1, '.', '');

            $loft->update([
                'ShfLenCd' => $ShfLenCd ?? null,
                'ShfLenDsc' => $request->ShfLenDsc_update ?? null,
                'ShfLenDiff' => $request->ShfLenDiff_update ?? null,
                'price' =>  isset($request->price)  ? $request->price : null,
            ]);

            DB::commit();

            return redirect()->route('shflen.index')->with([
                'title' => 'Done',
                'message' => 'ShfLen updated successfully.',
                'type' => 'success',
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->route('shflen.index')->with([
                'title' => 'Fail',
                'message' => 'Update failed: ' . $e->getMessage(),
                'type' => 'error',
            ]);
        }
    }

    public function destroy($id)
    {
        try {
            ShfLen::where('ShfLenId', $id)->delete();
            return redirect()->route('shflen.index')->with(['title' => 'Done', 'message' => 'ShfLen deleted successfully.', 'type' => 'success']);

        } catch (\Exception $e) {
            return redirect()->route('shflen.index')->with(['title' => 'Fail', 'message' => 'Unable to delete ShfLen.', 'type' => 'error']);
        }

    }

    public function updateStatus(Request $request, $id)
    {
        if ($request->method() == 'GET') {
            return response()->json([
                'success' => false,
                'message' => 'Invalid request method.',
            ], 400);
        }

        $gripSize = ShfLen::where('ShfLenId', $id)->firstOrFail();

        $request->validate([
            'status' => 'required|in:0,1',
        ], [
            'status.required' => 'Please select a status.',
            'status.in' => 'Status must be either Active (1) or Inactive (0).',
        ]);

        try {
            DB::beginTransaction();

            // Update status
            $gripSize->status = $request->status;
            $gripSize->save();

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Shaft Length status updated successfully.',
                'status' => $gripSize->status,
                'resultHtml' => ($gripSize->status == 1) ? '<span style="cursor:pointer;" class="success change-grip-size-status"  shtlen_id="'.$gripSize->ShfLenId.'" shtlen_status="0" >Active</span>' : '<span class="danger change-grip-size-status" shtlen_id="'.$gripSize->ShfLenId.'" shtlen_status="1"  style="cursor:pointer;">Inactive</span>'

            ], 200);
        } catch (\Exception $e) {
            DB::rollback();
            return response()->json([
                'success' => false,
                'message' => 'Unable to update Shaft Length status, please try again.',
                'error' => $e->getMessage(),
            ], 500);
        }
    }
}
