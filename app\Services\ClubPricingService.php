<?php

namespace App\Services;

use App\Models\HeadDsgn;
use App\Models\GripType;
use App\Models\GripSz;
use App\Models\Loft;
use App\Models\Lie;
use App\Models\FaceAngle;
use App\Models\Hossel;
use App\Models\ShfLen;
use App\Models\ShfType;
use App\Models\ShfFlx;
use App\Models\HeadWgtRng;
use Illuminate\Support\Facades\Auth;

class ClubPricingService
{
    /**
     * Calculate the price for a single club based on its components
     *
     * @param array $clubData - Club data containing component IDs
     * @return float - Total price for the club
     */
    public function calculateClubPrice(array $clubData): float
    {
        $totalPrice = 0;

        // Get model price (required)
        $modelPrice = $this->getModelPrice($clubData);
        $totalPrice += $modelPrice;

        // Add optional component prices
        $totalPrice += $this->getGripTypePrice($clubData);
        $totalPrice += $this->getGripSizePrice($clubData);
        $totalPrice += $this->getLoftPrice($clubData);
        $totalPrice += $this->getLiePrice($clubData);
        $totalPrice += $this->getFaceAnglePrice($clubData);
        $totalPrice += $this->getHosselPrice($clubData);

        // Add new pricing components
        $totalPrice += $this->getLengthPrice($clubData);
        $totalPrice += $this->getShaftMaterialPrice($clubData);
        $totalPrice += $this->getShaftFlexPrice($clubData);
        $totalPrice += $this->getShaftWeightPrice($clubData);

        return round($totalPrice, 2);
    }

    /**
     * Calculate total order amount from clubs array
     *
     * @param array $clubs - Array of club data
     * @return float - Total order amount
     */
    public function calculateOrderTotal(array $clubs): float
    {
        $totalAmount = 0;

        foreach ($clubs as $club) {
            $quantity = $club['category']['model']['quantity'] ?? 1;
            $clubPrice = $this->calculateClubPrice($club);
            $totalAmount += ($clubPrice * $quantity);
        }

        return round($totalAmount, 2);
    }

    /**
     * Get model price based on B2B/B2C logic
     *
     * @param array $clubData
     * @return float
     */
    private function getModelPrice(array $clubData): float
    {
        $modelId = $clubData['category']['model']['model_id'] ?? null;

        if (!$modelId) {
            return 0;
        }

        $model = HeadDsgn::find($modelId);

        if (!$model) {
            return 0;
        }

        // Determine if request is B2B or B2C
        $isB2B = $this->isB2BRequest();

        // Apply pricing logic based on model type
        switch ($model->type) {
            case 'b2b':
                return $isB2B ? ($model->b2b_price ?? 0) : 0;

            case 'b2c':
                return !$isB2B ? ($model->b2c_price ?? 0) : 0;

            case 'both':
                return $isB2B ? ($model->b2b_price ?? 0) : ($model->b2c_price ?? 0);

            default:
                return 0;
        }
    }

    /**
     * Get grip type price
     *
     * @param array $clubData
     * @return float
     */
    private function getGripTypePrice(array $clubData): float
    {
        $gripTypeId = $clubData['grip']['type'] ?? null;

        if (!$gripTypeId) {
            return 0;
        }

        $gripType = GripType::find($gripTypeId);

        return $gripType ? ($gripType->price ?? 0) : 0;
    }

    /**
     * Get grip size price
     *
     * @param array $clubData
     * @return float
     */
    private function getGripSizePrice(array $clubData): float
    {
        $gripSizeId = $clubData['grip']['size'] ?? null;

        if (!$gripSizeId) {
            return 0;
        }

        $gripSize = GripSz::find($gripSizeId);

        return $gripSize ? ($gripSize->price ?? 0) : 0;
    }

    /**
     * Get loft price
     *
     * @param array $clubData
     * @return float
     */
    private function getLoftPrice(array $clubData): float
    {
        $loftId = $clubData['category']['model']['loft_id'] ?? null;

        if (!$loftId) {
            return 0;
        }

        $loft = Loft::find($loftId);

        return $loft ? ($loft->price ?? 0) : 0;
    }

    /**
     * Get lie price
     *
     * @param array $clubData
     * @return float
     */
    private function getLiePrice(array $clubData): float
    {
        $lieId = $clubData['category']['model']['lie_angle_id'] ?? null;

        if (!$lieId) {
            return 0;
        }

        $lie = Lie::find($lieId);

        return $lie ? ($lie->price ?? 0) : 0;
    }

    /**
     * Get face angle price
     *
     * @param array $clubData
     * @return float
     */
    private function getFaceAnglePrice(array $clubData): float
    {
        $faceAngleId = $clubData['category']['model']['faceangle_id'] ?? null;

        if (!$faceAngleId) {
            return 0;
        }

        $faceAngle = FaceAngle::find($faceAngleId);

        return $faceAngle ? ($faceAngle->price ?? 0) : 0;
    }

    /**
     * Get hossel price
     *
     * @param array $clubData
     * @return float
     */
    private function getHosselPrice(array $clubData): float
    {
        $hosselId = $clubData['category']['model']['hossel_id'] ?? null;

        if (!$hosselId) {
            return 0;
        }

        $hossel = Hossel::find($hosselId);

        return $hossel ? ($hossel->price ?? 0) : 0;
    }

    /**
     * Determine if the current request is B2B or B2C
     * Based on user role and account type
     *
     * @return bool - true if B2B, false if B2C
     */
    private function isB2BRequest(): bool
    {
        $user = Auth::user();

        if (!$user) {
            return false; // Default to B2C if no user
        }

        // Check if user has fitter role (B2B)
        if ($user->hasRole('fitter') || $user->hasRole('admin')) {
            return true;
        }

        // Check if user has customer role (B2C)
        if ($user->hasRole('customer')) {
            return false;
        }

        // Default to B2C for other roles
        return false;
    }

    /**
     * Get length price from database
     *
     * @param array $clubData
     * @return float
     */
    private function getLengthPrice(array $clubData): float
    {
        $lengthId = $clubData['lengthData']['range'] ?? null;

        if (!$lengthId) {
            return 0;
        }

        $length = ShfLen::find($lengthId);

        if (!$length) {
            return 0;
        }

        return $length->price ?? 0;
    }

    /**
     * Get shaft material price from database
     *
     * @param array $clubData
     * @return float
     */
    private function getShaftMaterialPrice(array $clubData): float
    {
        $shaftTypeId = $clubData['category']['shaft']['material'] ?? null;

        if (!$shaftTypeId) {
            return 0;
        }

        $shaftType = ShfType::find($shaftTypeId);

        if (!$shaftType) {
            return 0;
        }

        return $shaftType->price ?? 0;
    }

    /**
     * Get shaft flex price from database
     *
     * @param array $clubData
     * @return float
     */
    private function getShaftFlexPrice(array $clubData): float
    {
        $shaftFlexId = $clubData['category']['shaft']['flex'] ?? null;

        if (!$shaftFlexId) {
            return 0;
        }

        $shaftFlex = ShfFlx::find($shaftFlexId);

        if (!$shaftFlex) {
            return 0;
        }

        return $shaftFlex->price ?? 0;
    }

    /**
     * Get shaft weight price from database
     *
     * @param array $clubData
     * @return float
     */
    private function getShaftWeightPrice(array $clubData): float
    {
        $weightRangeId = $clubData['category']['shaft']['weight'] ?? null;

        if (!$weightRangeId) {
            return 0;
        }

        $weightRange = HeadWgtRng::find($weightRangeId);

        if (!$weightRange) {
            return 0;
        }

        return $weightRange->price ?? 0;
    }

    /**
     * Get pricing breakdown for debugging/display purposes
     *
     * @param array $clubData
     * @return array
     */
    public function getPricingBreakdown(array $clubData): array
    {
        return [
            'model_price' => $this->getModelPrice($clubData),
            'grip_type_price' => $this->getGripTypePrice($clubData),
            'grip_size_price' => $this->getGripSizePrice($clubData),
            'loft_price' => $this->getLoftPrice($clubData),
            'lie_price' => $this->getLiePrice($clubData),
            'face_angle_price' => $this->getFaceAnglePrice($clubData),
            'hossel_price' => $this->getHosselPrice($clubData),
            'length_price' => $this->getLengthPrice($clubData),
            'shaft_material_price' => $this->getShaftMaterialPrice($clubData),
            'shaft_flex_price' => $this->getShaftFlexPrice($clubData),
            'shaft_weight_price' => $this->getShaftWeightPrice($clubData),
            'total_price' => $this->calculateClubPrice($clubData),
            'is_b2b' => $this->isB2BRequest()
        ];
    }
}
