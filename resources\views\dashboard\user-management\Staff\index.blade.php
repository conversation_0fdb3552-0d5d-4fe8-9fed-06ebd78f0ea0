@extends('theme.layout.master')

@push('css')
@endpush

@section('content')
    <section class="main-dashboard">
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-12">
                    <div class="white_box table_box">
                        <div class="table_header">
                            <div class="user_engagement">
                                <h5>Staff</h5>
                            </div>
                            <div class="side_fields custom_entries">
                                <div class="custom_action_fields">
                                    <div class="custom_search_box">
                                        <form>
                                            <div class="txt_field">
                                                <i class="fa-solid fa-magnifying-glass"></i>
                                                <input type="search" placeholder="Search" class="form-control searchinput">
                                            </div>
                                        </form>
                                    </div>
                                    <!-- Filter Dropdown -->
                                    <div class="dropdown-btn">
                                        <button type="button" class="btn dropdown-toggle light_green_btn"
                                                id="filterDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                            <i class="fas fa-filter"></i> Filter
                                        </button>
                                        <div class="dropdown-menu filter-dropdown" aria-labelledby="filterDropdown">
                                            <div class="dropdown_top">
                                                <h6 class="">Filter</h6>
                                                <button type="button" class="btn_close" data-bs-dismiss="dropdown"
                                                        aria-label="Close">
                                                    <i class="fas fa-times"></i>
                                                </button>
                                            </div>

                                            @include('theme.layout.partials.status_category_form_filter',['roles' => $roles,'status' => true,'category' => false])

                                        </div>
                                    </div>
                                    <!-- Create Button -->
                                    <a href="{{ route('staffs.create') }}" class="btn dark_green_btn"><i
                                            class="fa-solid fa-square-plus"></i>Create</a>
                                </div>

                            </div>
                            <div class="status_buttons">
                                <button id="activateSelected" class="btn btn-success" title="Active">  <i class="fas fa-check-circle"></i> </button>
                                <button id="deactivateSelected" class="btn btn-danger" title="Deactive">     <i class="fas fa-ban"></i> </button>
                            </div>
                        </div>

                        <div class="table-responsive">
                            <table class="staffs-datatable table table-row-bordered gy-5">
                                <thead>
                                <tr>
                                    <th>
                                        <input type="checkbox" class="select_all_checkboxes">
                                    </th>
                                    <th>SR#</th>
                                    <th>Staff Name</th>
                                    <th>Role</th>
                                    <th>Email</th>
                                    <th>Phone#</th>
                                    <th>Status</th>
                                    <th>Action</th>
                                </tr>
                                </thead>
                                {{--                                <tbody>--}}
                                {{--                                @for ($i = 1; $i <= 10; $i++)--}}
                                {{--                                    <tr>--}}
                                {{--                                        <td>01</td>--}}
                                {{--                                        <td class="rounded-start">--}}
                                {{--                                            <img class="img-fluid" src="{{ asset('website') }}/assets/images/john.png"/>--}}
                                {{--                                            <div class="">John Doe</div>--}}
                                {{--                                        </td>--}}
                                {{--                                        <td>Course Manager</td>--}}
                                {{--                                        <td><EMAIL></td>--}}
                                {{--                                        <td>(+145) 1254 1254 14</td>--}}
                                {{--                                        <td class="text_success">Active</td>--}}
                                {{--                                        <td>--}}
                                {{--                                            <div class="dropdown">--}}
                                {{--                                                <button class="btn dropdown-toggle" type="button"--}}
                                {{--                                                        id="dropdownMenuButton11" data-bs-toggle="dropdown"--}}
                                {{--                                                        aria-expanded="false">--}}
                                {{--                                                    <img class="ellipsis_img"--}}
                                {{--                                                         src="{{ asset('website') }}/assets/images/ellipsis.svg">--}}
                                {{--                                                </button>--}}
                                {{--                                                <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton11">--}}
                                {{--                                                    <li><a class="dropdown-item"--}}
                                {{--                                                           href="{{ route('staffs.show' , ['id' , 0]) }}">View</a>--}}
                                {{--                                                    </li>--}}
                                {{--                                                    <li><a class="dropdown-item"--}}
                                {{--                                                           href="{{ route('staffs.edit', ['id' , 0]) }}">Edit</a></li>--}}
                                {{--                                                </ul>--}}
                                {{--                                            </div>--}}
                                {{--                                        </td>--}}
                                {{--                                    </tr>--}}
                                {{--                                @endfor--}}
                                {{--                                </tbody>--}}
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

@endsection

@push('js')
    <script>
        $(document).ready(function () {
            // Create custom page length dropdown with 10, 25, 50, and All options
            var htmlContent = '<div class="dt-layout-cell dt-layout-start"><div class="dt-length"><label for="dt-length-0">Show</label><select aria-controls="DataTables_Table_0" class="dt-input" id="dt-length-0"><option value="10">10</option><option value="25">25</option><option value="50">50</option><option value="-1">All</option></select><label for="dt-length-0">entries</label></div></div>';

            // Append the dropdown to the side fields container
            document.querySelector('.side_fields.custom_entries').insertAdjacentHTML('afterbegin', htmlContent);

            var table = $('.staffs-datatable').DataTable({
                processing: true,
                serverSide: true,
                ajax: {
                    url: '{{ route('staffs.index') }}',
                    type: 'GET',
                    data: function (d) {
                        d.status = $('#status').val();
                        d.role = $('#role').val();
                    },
                    dataSrc: 'data'
                },
                columns: [
                    { data: 'checkbox', name: 'checkbox' },
                    {data: null, name: null, defaultContent: ''},
                    {data: 'staff_name', name: 'staff_name'},
                    {data: 'role', name: 'role'},
                    {data: 'email', name: 'email'},
                    {data: 'phone', name: 'phone'},
                    {data: 'status', name: 'status'},
                    {data: 'action', name: 'action', orderable: false, searchable: false}
                ],
                order: [[1, 'desc']],
                pageLength: 10,
                lengthChange: false,
                ordering: false,
                searching: true,
                createdRow: function (row, data, dataIndex) {
                    $(row).find('td:eq(1)').html(dataIndex + 1);
                },
                initComplete: function () {
                    $('[data-bs-toggle="tooltip"]').tooltip();
                }
            });

            // Change the page length based on dropdown selection
            document.querySelector('#dt-length-0').addEventListener('change', function() {
                var selectedValue = this.value;
                if (selectedValue == "-1") {
                    table.page.len(-1).draw(); // Show all records
                } else {
                    table.page.len(parseInt(selectedValue)).draw(); // Set page length to selected value
                }
            });
            $(document).on("input", '.searchinput', function () {
                var searchValue = $(this).val();
                table.search(searchValue).draw();
            });

            $(".filter-form").submit(function (e) {
                e.preventDefault();
                table.draw();
            });

            // Select All functionality
            $('.select_all_checkboxes').on('click', function () {
                var checkboxes = $('.category-checkbox');
                var allChecked = checkboxes.filter(':checked').length === checkboxes.length;
                checkboxes.prop('checked', !allChecked);
                $(this).prop('checked', !allChecked);
            });

            $(document).on('click', '.category-checkbox', function () {
                var checkboxes = $('.category-checkbox');
                var allChecked = checkboxes.filter(':checked').length === checkboxes.length;
                $('.select_all_checkboxes').prop('checked', allChecked);
            });

        });

        $(document).on('click', '.change-staff-status', function () {
            var staffId = $(this).attr('staff_id');
            var status = $(this).attr('staff_status');
            var current_parent = $(this).parent();
            Swal.fire({
                title: 'Are you sure?',
                text: 'You are about to change the status.',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: 'Yes, change it!'
            }).then((result) => {
                if (result.isConfirmed) {
                    var url = "{{ url('staffs') }}/" + staffId + "/status";
                    $.ajax({
                        type: "POST",
                        url: url,
                        data: {
                            _token: "{{ csrf_token() }}",
                            status: status
                        },
                        success: function (response) {
                            if (response.success) {
                                current_parent.html(response.resultHtml);
                                swal.fire({
                                    icon: 'success',
                                    title: 'Success',
                                    text: response.message,
                                    timer: 4000,
                                });
                            } else {
                                swal.fire({
                                    icon: 'error',
                                    title: 'Error',
                                    text: response.message,
                                    timer: 4000,

                                });
                            }
                        },
                        error: function (xhr, status, error) {
                            swal.fire({
                                icon: 'error',
                                title: 'Error',
                                text: 'An error occurred while changing the status.',
                                timer: 4000,
                            });
                        }
                    });
                }
            });
        });

    </script>
@endpush
