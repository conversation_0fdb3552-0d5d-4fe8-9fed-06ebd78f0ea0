<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
class HeadDsgn extends Model
{
    use HasFactory;
    use softDeletes;
    protected $table = 'headdsgn';
    protected $guarded = [];
    protected $primaryKey = 'HeadDsgnId';

    public function category(){
        return $this->belongsTo(ClbType::class,'ClbTypeCd', 'ClbTypeCd');
    }

    public function getStatusHtmlAttribute()
    {
        return $this->status == 1 ? '<span style="cursor:pointer;" class="success change-model-status"  model_id="'.$this->HeadDsgnId.'" model_status="0" >Active</span>' : '<span class="danger change-model-status" model_id="'.$this->HeadDsgnId.'" model_status="1"  style="cursor:pointer;">Inactive</span>';
    }

    public function image()
    {
        try{
            return json_decode($this->images)[0];
        } catch (\Exception $e) {
            return 'models/default.png';
        }
    }
    public function getImageAttribute()
    {
        try {
            return json_decode($this->images)[0];
        } catch (\Exception $e) {
            return 'models/default.png';
        }
    }
    public function images()
    {
        try{
            return json_decode($this->images);
        } catch (\Exception $e) {
            return ['models/default.png'];
        }
    }

    public function colors()
    {
        try{
            return $this->colors != null ? json_decode($this->colors) : [];
        } catch (\Exception $e) {
            return ['#000000'];
        }
    }
    public function club_numbers()
    {
        try{
            return $this->club_numbers != null ? json_decode($this->club_numbers) : [];

        } catch (\Exception $e) {
            return [];
        }
    }

    public function hossels()
    {
        return $this->hasMany(Hossel::class, 'category_id', 'ClbTypeCd');
    }
}
