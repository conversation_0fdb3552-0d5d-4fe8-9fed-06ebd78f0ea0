<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class BillCd extends Model
{
    use HasFactory , softDeletes;

    protected $table = 'billcd';
    protected $guarded = [];
    protected $primaryKey = 'BillCd';
    protected $keyType = 'string';

    /**
     * Check if bill code is valid for discount
     */
    public function isValidForDiscount()
    {
        // Assuming CurrInd field indicates if the bill code is active
        return isset($this->CurrInd) ? $this->CurrInd == 1 : true;
    }

    /**
     * Calculate discount based on bill code
     * Uses Disc field for percentage discount
     */
    public function calculateDiscount($orderAmount)
    {
        if (!$this->isValidForDiscount()) {
            return 0;
        }

        // Use Disc field for percentage discount
        if (isset($this->Disc) && $this->Disc > 0) {
            return ($orderAmount * $this->Disc) / 100;
        }

        return 0;
    }

    /**
     * Get discount percentage for display
     */
    public function getDiscountPercentage()
    {
        return isset($this->Disc) ? $this->Disc : 0;
    }
}
