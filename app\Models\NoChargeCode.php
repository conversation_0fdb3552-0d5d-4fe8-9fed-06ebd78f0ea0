<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
class NoChargeCode extends Model
{
    use HasFactory , softDeletes;

    protected $table = 'compcd';
    protected $guarded = [];
    protected $primaryKey = 'CompCdId';
    protected $keyType = 'string';

    /**
     * Check if comp code is valid for 100% discount
     */
    public function isValidForDiscount()
    {
        // Assuming CurrInd field indicates if the comp code is active
        return isset($this->CurrInd) ? $this->CurrInd == 1 : true;
    }

    /**
     * Calculate discount - always 100% for comp codes
     */
    public function calculateDiscount($orderAmount)
    {
        if (!$this->isValidForDiscount()) {
            return 0;
        }

        // Comp codes give 100% discount
        return $orderAmount;
    }

    /**
     * Get discount percentage - always 100% for comp codes
     */
    public function getDiscountPercentage()
    {
        return 100;
    }
}
