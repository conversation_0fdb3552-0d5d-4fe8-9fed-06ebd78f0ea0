<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;

use App\Models\ClbType;
use App\Models\ShfFlx;
use App\Models\ShfType;
use DB;
use Illuminate\Http\Request;
use Yajra\DataTables\Facades\DataTables;

class ShfFlxController extends Controller
{
    public function __construct()
    {
        $categories = ClbType::latest()->get();
        view()->share('categories', $categories);
    }

    public function index(Request $request)
    {
        if ($request->ajax()) {
            $shaftQuery = ShfFlx::latest();
            if ($request->has('search') && $request->search['value'] != '') {
                $searchValue = $request->search['value'];
                $shaftQuery->where(function ($query) use ($searchValue) {
                    $query->where('ShfFlxDsc', 'like', "%$searchValue%")
                        ->orWhere('ShfFlxCd', 'like', "%$searchValue%");
                });
            }
            if ($request->has('status') && $request->status != '') {
                $shaftQuery->where('status', $request->status == 'active' ? '1' : '0');
            }

            return DataTables::of($shaftQuery)
                ->addColumn('checkbox', function ($row) {
                    return '<input type="checkbox" class="category-checkbox" value="' . $row->ShfFlxCd . '">';
                })
                ->addColumn('name', function ($row) {
                    return $row->ShfFlxDsc ?? 'N/A';
                })
                ->addColumn('price', function ($row) {
                    return $row->price ?? '0';
                })
                ->addColumn('status', function ($row) {
                    return $row->statusHtml ??'';
                })
                ->addColumn('action', function ($row) {
                    return '
                <div class="dropdown">
                    <button class="btn dropdown-toggle" type="button" id="dropdownMenuButton11" data-bs-toggle="dropdown" aria-expanded="false">
                        <img class="ellipsis_img" src="' . asset('website') . '/assets/images/ellipsis.svg">
                    </button>
                    <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton11">
                         <li><a class="dropdown-item edit_model_button" href="javascript:void(0)"
                                       model_id="' . $row->ShfFlxCd . '"
                                       model_price="' . $row->price . '"
                                       model_ShfFlxDsc="' . $row->ShfFlxDsc . '">Edit</a></li>
                                <li>
                                <form action="' . route('shf-flx.destroy', $row->ShfFlxCd) . '" method="POST" class="delete-form">
                                    ' . csrf_field() . method_field('DELETE') . '
                                    <a class="dropdown-item" href="javascript:void(0)" onclick="showDeleteConfirmation(this)">Delete</a>
                                </form>
                    </ul>
                </div>';
                })
                ->rawColumns(['status', 'action','checkbox'])
                ->make(true);
        }

        return view('dashboard.Cruds.Flexes.index');
    }

    public function create()
    {
        //
    }

    public function store(Request $request)
    {
        extract($request->all());
        $request->validate([
            'ShfFlxDsc' => 'required|string',
        ], [
            'ShfFlxDsc.required' => 'Flex name is required.',
        ]);
        try {
            DB::beginTransaction();
            $model = ShfFlx::create([
                'ShfFlxCd' => time(),
                'ShfFlxDsc' => $ShfFlxDsc,
                'price' =>  isset($request->price)  ? $request->price : null,
            ]);
            DB::commit();
            if ($model) {
                return redirect(url('shf-flx'))->with(['title' => 'Done', 'message' => 'shaft flex created successfully.', 'type' => 'success']);
            } else {
                return redirect(url('shf-flx'))->with(['title' => 'Fail', 'message' => 'Unable to create shaft flex.', 'type' => 'error']);
            }
        } catch (\Exception $e) {
            DB::rollback();
            return redirect(url('shf-flx'))->with(['title' => 'Fail', 'message' => 'Unable to create shaft, please try again.' . $e->getMessage(), 'type' => 'error']);
        }

    }

    public function show($id)
    {
        //
    }

    public function edit($id)
    {
        //
    }

    public function update(Request $request, $shaftId = null)
    {
        $shaft = ShfFlx::where('ShfFlxCd', $request->model_id_update)->firstOrFail();
        $request->validate([
            'ShfFlxDsc' => 'required|string',
        ], [
            'ShfFlxDsc.required' => 'Flex name is required.',
        ]);

        try {
            DB::beginTransaction();

            $shaft->update([
                'ShfFlxDsc' => $request->ShfFlxDsc ?? $shaft->ShfFlxDsc,
                'price' =>  isset($request->price)  ? $request->price : null,
            ]);
            DB::commit();
            return redirect()->back()->with([ 'title' => 'Done', 'type' => 'success', 'message' => 'Shaft Flex Successfully']);
        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->back()->with('error', $e->getMessage());

        }
    }

    public function destroy($id)
    {
        try {
            ShfFlx::where('ShfFlxCd', $id)->update(['deleted_at' => now()]);
            return redirect()->route('shf-flx.index')->with(['title' => 'Done', 'message' => 'Shaft Flex deleted successfully.', 'type' => 'success']);
        } catch (\Exception $e) {
            return redirect()->route('shf-flx.index')->with(['title' => 'Fail', 'message' => 'Unable to delete shaft flex.', 'type' => 'error']);
        }
    }

    public function updateStatus(Request $request, $id)
    {
        if ($request->method() == 'GET') {
            return response()->json([
                'success' => false,
                'message' => 'Invalid request method.',
            ], 400);
        }

        $model = ShfFlx::where('ShfFlxCd', $id)->firstOrFail();

        $request->validate([
            'status' => 'required|in:0,1',
        ], [
            'status.required' => 'Please select a status.',
            'status.in' => 'Status must be either Active (1) or Inactive (0).',
        ]);

        try {
            DB::beginTransaction();

            // Update status
            $model->status = $request->status;
            $model->save();

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Shaft FLex status updated successfully.',
                'status' => $model->status,
                'resultHtml' => ($model->status == 1) ? '<span style="cursor:pointer;" class="success change-model-status"  model_id="' . $model->ShfFlxCd . '" model_status="0" >Active</span>' : '<span style="cursor:pointer;" class="danger change-model-status"  model_id="' . $model->ShfFlxCd . '" model_status="1" >Inactive</span>'
            ], 200);
        } catch (\Exception $e) {
            DB::rollback();
            return response()->json([
                'success' => false,
                'message' => 'Unable to update Shaft FLex status, please try again.',
                'error' => $e->getMessage(),
            ], 500);
        }
    }
}
