<?php

namespace App\Traits;

use App\Services\StaxService;

trait StaxTrait
{
    /**
     * Get Stax service instance
     *
     * @return StaxService
     */
    protected function getStaxService(): StaxService
    {
        return new StaxService();
    }

    /**
     * Create a customer in Stax
     *
     * @param array $customerData
     * @return array
     */
    protected function createStaxCustomer(array $customerData): array
    {
        // Since we're passing pre-formatted data from trait, we'll call the API directly
        try {
            $response = \Illuminate\Support\Facades\Http::withHeaders([
                'Authorization' => 'Bearer ' . env('STAX_API_KEY'),
                'Accept' => 'application/json',
                'Content-Type' => 'application/json',
            ])->post('https://apiprod.fattlabs.com/customer', $customerData);

            if ($response->successful()) {
                \Illuminate\Support\Facades\Log::info('Stax customer created successfully', [
                    'customer_data' => $customerData,
                    'response' => $response->json()
                ]);

                return [
                    'success' => true,
                    'data' => $response->json(),
                    'status_code' => $response->status()
                ];
            } else {
                \Illuminate\Support\Facades\Log::error('Failed to create Stax customer', [
                    'customer_data' => $customerData,
                    'response' => $response->json(),
                    'status_code' => $response->status()
                ]);

                return [
                    'success' => false,
                    'error' => $response->json(),
                    'status_code' => $response->status()
                ];
            }
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Exception occurred while creating Stax customer', [
                'customer_data' => $customerData,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'error' => ['message' => 'An error occurred while creating customer: ' . $e->getMessage()],
                'status_code' => 500
            ];
        }
    }

    /**
     * Update a customer in Stax
     *
     * @param string $customerId
     * @param array $customerData
     * @return array
     */
    protected function updateStaxCustomer(string $customerId, array $customerData): array
    {
        return $this->getStaxService()->updateCustomer($customerId, $customerData);
    }

    /**
     * Get a customer from Stax
     *
     * @param string $customerId
     * @return array
     */
    protected function getStaxCustomer(string $customerId): array
    {
        return $this->getStaxService()->getCustomer($customerId);
    }

    /**
     * Delete a customer from Stax
     *
     * @param string $customerId
     * @return array
     */
    protected function deleteStaxCustomer(string $customerId): array
    {
        return $this->getStaxService()->deleteCustomer($customerId);
    }

    /**
     * Validate customer data for Stax
     *
     * @param array $customerData
     * @return array
     */
    protected function validateStaxCustomerData(array $customerData): array
    {
        return $this->getStaxService()->validateCustomerData($customerData);
    }

    /**
     * Handle Stax customer creation with validation and error handling
     *
     * @param array $customerData
     * @return \Illuminate\Http\JsonResponse
     */
    protected function handleStaxCustomerCreation(array $customerData): \Illuminate\Http\JsonResponse
    {
        // Validate customer data
        $validation = $this->validateStaxCustomerData($customerData);

        if (!$validation['valid']) {
            return response()->json([
                'success' => false,
                'error' => $validation['message']
            ], 422);
        }

        // Create customer in Stax
        $result = $this->createStaxCustomer($customerData);

        if ($result['success']) {
            return response()->json([
                'success' => true,
                'data' => $result['data']
            ], $result['status_code']);
        } else {
            return response()->json([
                'success' => false,
                'error' => $result['error']
            ], $result['status_code']);
        }
    }

    /**
     * Handle Stax customer update with validation and error handling
     *
     * @param string $customerId
     * @param array $customerData
     * @return \Illuminate\Http\JsonResponse
     */
    protected function handleStaxCustomerUpdate(string $customerId, array $customerData): \Illuminate\Http\JsonResponse
    {
        // Validate customer data
        $validation = $this->validateStaxCustomerData($customerData);

        if (!$validation['valid']) {
            return response()->json([
                'success' => false,
                'error' => $validation['message']
            ], 422);
        }

        // Update customer in Stax
        $result = $this->updateStaxCustomer($customerId, $customerData);

        if ($result['success']) {
            return response()->json([
                'success' => true,
                'data' => $result['data']
            ], $result['status_code']);
        } else {
            return response()->json([
                'success' => false,
                'error' => $result['error']
            ], $result['status_code']);
        }
    }

    /**
     * Create account and Stax customer together
     *
     * @param \Illuminate\Http\Request $request
     * @param object $account - The created account object
     * @return array
     */
    protected function createAccountWithStaxCustomer($request, $account): array
    {
        // Prepare customer data for Stax
        $customerData = [
            'firstname' => $request->first_name,
            'lastname' => $request->last_name,
            'company' => $request->account_name,
            'email' => $request->email,
            'phone' => preg_replace('/\D/', '', $request->phone),
            'address_1' => $request->location,
            'address_2' => $request->location,
            'address_city' => $request->city,
            'address_state' => $request->state,
            'address_zip' => $request->zip_code,
            'address_country' => $this->convertToThreeCharCountryCode($request->country),
            'reference' => 'Henry'
        ];
//        dd($customerData);
        // Create customer in Stax
        $staxResult = $this->createStaxCustomer($customerData);

        if ($staxResult['success']) {
            // Store Stax customer ID in account if needed
            // You can add a stax_customer_id field to your accounts table
             $account->StaxCustomerId = $staxResult['data']['id'] ?? null;
             $account->save();

            return [
                'success' => true,
                'account' => $account,
                'stax_customer' => $staxResult['data'],
                'message' => 'Account and Stax customer created successfully'
            ];
        } else {
            return [
                'success' => false,
                'account' => $account,
                'stax_error' => $staxResult['error'],
                'message' => 'Account created but failed to create Stax customer'
            ];
        }
    }

    /**
     * Add payment method to Stax customer
     *
     * @param string $customerId
     * @param array $paymentData
     * @return array
     */
    protected function addStaxPaymentMethod(string $customerId, array $paymentData): array
    {
        return $this->getStaxService()->addPaymentMethod($customerId, $paymentData);
    }

    /**
     * Get payment methods for Stax customer
     *
     * @param string $customerId
     * @return array
     */
    protected function getStaxPaymentMethods(string $customerId): array
    {
        return $this->getStaxService()->getPaymentMethods($customerId);
    }

    /**
     * Delete payment method from Stax
     *
     * @param string $customerId
     * @param string $paymentMethodId
     * @return array
     */
    protected function deleteStaxPaymentMethod(string $customerId, string $paymentMethodId): array
    {
        return $this->getStaxService()->deletePaymentMethod($customerId, $paymentMethodId);
    }

    /**
     * Validate payment method data
     *
     * @param array $paymentData
     * @return array
     */
    protected function validateStaxPaymentData(array $paymentData): array
    {
        return $this->getStaxService()->validatePaymentData($paymentData);
    }

    /**
     * Handle payment method creation with validation and error handling
     *
     * @param string $customerId
     * @param array $paymentData
     * @return \Illuminate\Http\JsonResponse
     */
    protected function handleStaxPaymentMethodCreation(string $customerId, array $paymentData): \Illuminate\Http\JsonResponse
    {
        // Validate payment data
        $validation = $this->validateStaxPaymentData($paymentData);

        if (!$validation['valid']) {
            return response()->json([
                'success' => false,
                'error' => $validation['message']
            ], 422);
        }

        // Add payment method to Stax
        $result = $this->addStaxPaymentMethod($customerId, $paymentData);

        if ($result['success']) {
            return response()->json([
                'success' => true,
                'data' => $result['data']
            ], $result['status_code']);
        } else {
            return response()->json([
                'success' => false,
                'error' => $result['error']
            ], $result['status_code']);
        }
    }

    /**
     * Convert country code/name to 3-character ISO code for Stax API
     *
     * @param string $country
     * @return string
     */
    private function convertToThreeCharCountryCode($country): string
    {
        if (empty($country)) {
            return 'USA'; // Default to USA
        }

        // Convert to uppercase for comparison
        $country = strtoupper(trim($country));

        // Common country code mappings
        $countryMappings = [
            // 2-char to 3-char ISO codes
            'US' => 'USA',
            'CA' => 'CAN',
            'GB' => 'GBR',
            'UK' => 'GBR',
            'AU' => 'AUS',
            'DE' => 'DEU',
            'FR' => 'FRA',
            'IT' => 'ITA',
            'ES' => 'ESP',
            'NL' => 'NLD',
            'BE' => 'BEL',
            'CH' => 'CHE',
            'AT' => 'AUT',
            'SE' => 'SWE',
            'NO' => 'NOR',
            'DK' => 'DNK',
            'FI' => 'FIN',
            'IE' => 'IRL',
            'PT' => 'PRT',
            'JP' => 'JPN',
            'KR' => 'KOR',
            'CN' => 'CHN',
            'IN' => 'IND',
            'BR' => 'BRA',
            'MX' => 'MEX',
            'AR' => 'ARG',
            'CL' => 'CHL',
            'CO' => 'COL',
            'PE' => 'PER',
            'ZA' => 'ZAF',
            'EG' => 'EGY',
            'NG' => 'NGA',
            'KE' => 'KEN',
            'MA' => 'MAR',
            'TN' => 'TUN',
            'GH' => 'GHA',
            'SN' => 'SEN',
            'RU' => 'RUS',
            'UA' => 'UKR',
            'PL' => 'POL',
            'CZ' => 'CZE',
            'HU' => 'HUN',
            'RO' => 'ROU',
            'BG' => 'BGR',
            'HR' => 'HRV',
            'SI' => 'SVN',
            'SK' => 'SVK',
            'LT' => 'LTU',
            'LV' => 'LVA',
            'EE' => 'EST',
            'TR' => 'TUR',
            'GR' => 'GRC',
            'CY' => 'CYP',
            'MT' => 'MLT',
            'IS' => 'ISL',
            'LU' => 'LUX',
            'MC' => 'MCO',
            'AD' => 'AND',
            'SM' => 'SMR',
            'VA' => 'VAT',
            'LI' => 'LIE',

            // Full country names to 3-char codes
            'UNITED STATES' => 'USA',
            'UNITED STATES OF AMERICA' => 'USA',
            'CANADA' => 'CAN',
            'UNITED KINGDOM' => 'GBR',
            'GREAT BRITAIN' => 'GBR',
            'ENGLAND' => 'GBR',
            'SCOTLAND' => 'GBR',
            'WALES' => 'GBR',
            'NORTHERN IRELAND' => 'GBR',
            'AUSTRALIA' => 'AUS',
            'GERMANY' => 'DEU',
            'FRANCE' => 'FRA',
            'ITALY' => 'ITA',
            'SPAIN' => 'ESP',
            'NETHERLANDS' => 'NLD',
            'BELGIUM' => 'BEL',
            'SWITZERLAND' => 'CHE',
            'AUSTRIA' => 'AUT',
            'SWEDEN' => 'SWE',
            'NORWAY' => 'NOR',
            'DENMARK' => 'DNK',
            'FINLAND' => 'FIN',
            'IRELAND' => 'IRL',
            'PORTUGAL' => 'PRT',
            'JAPAN' => 'JPN',
            'SOUTH KOREA' => 'KOR',
            'CHINA' => 'CHN',
            'INDIA' => 'IND',
            'BRAZIL' => 'BRA',
            'MEXICO' => 'MEX',
            'ARGENTINA' => 'ARG',
            'CHILE' => 'CHL',
            'COLOMBIA' => 'COL',
            'PERU' => 'PER',
            'SOUTH AFRICA' => 'ZAF',
            'EGYPT' => 'EGY',
            'NIGERIA' => 'NGA',
            'KENYA' => 'KEN',
            'MOROCCO' => 'MAR',
            'TUNISIA' => 'TUN',
            'GHANA' => 'GHA',
            'SENEGAL' => 'SEN',
            'RUSSIA' => 'RUS',
            'UKRAINE' => 'UKR',
            'POLAND' => 'POL',
            'CZECH REPUBLIC' => 'CZE',
            'HUNGARY' => 'HUN',
            'ROMANIA' => 'ROU',
            'BULGARIA' => 'BGR',
            'CROATIA' => 'HRV',
            'SLOVENIA' => 'SVN',
            'SLOVAKIA' => 'SVK',
            'LITHUANIA' => 'LTU',
            'LATVIA' => 'LVA',
            'ESTONIA' => 'EST',
            'TURKEY' => 'TUR',
            'GREECE' => 'GRC',
            'CYPRUS' => 'CYP',
            'MALTA' => 'MLT',
            'ICELAND' => 'ISL',
            'LUXEMBOURG' => 'LUX',
            'MONACO' => 'MCO',
            'ANDORRA' => 'AND',
            'SAN MARINO' => 'SMR',
            'VATICAN' => 'VAT',
            'LIECHTENSTEIN' => 'LIE',
        ];

        // Check if it's already a 3-character code
        if (strlen($country) === 3) {
            return $country;
        }

        // Look up in mappings
        if (isset($countryMappings[$country])) {
            return $countryMappings[$country];
        }

        // Default to USA if not found
        return 'USA';
    }
}
