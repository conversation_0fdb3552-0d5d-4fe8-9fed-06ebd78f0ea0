{{-- Custom Sidebar --}}
<aside class="app_sidebar">
    <div class="custom_sidebar">
        <a class="sidebar_brand" href="{{url('/')}}">
            <div class="site_logo">
                <img src="{{ asset('') }}{{ App\Models\Setting::first()->logo ?? '' }}" class="img-fluid" alt="Logo">
            </div>
        </a>
        <nav class="sidebar_nav_container">
            <ul class="sidebar_menus">
                <li class="menu_content @if(request()->is("home")) active @endif">
                    <a href="{{url('home')}}" class="menu_link">
                        <div class="dashboard_icon">
                            <img src="{{ asset('website') }}/assets/images/dashboard.svg">
                        </div>
                        Dashboard
                    </a>
                </li>
                @can('account-list')
                    <li class="menu_content @if(request()->is("accounts*")) active @endif">
                        <a href="{{ url('accounts') }}" class="menu_link">
                            <div class="dashboard_icon">
                                <img src="{{ asset('website') }}/assets/images/Golf_sidebar_img.svg">
                            </div>
                            Accounts/GolfCourse
                        </a>
                    </li>
                @endcan
                @if (auth()->user()->can('fitter-list') || auth()->user()->can('staff-list') || auth()->user()->can('customer-list'))
                    <li class="sidebar_dropdown @if(request()->is("fitters*") || request()->is("staff*") || request()->is("customers*")) active @endif">
                        <a class="menu_link dropdown_link" href="#UserCollapse" data-bs-toggle="collapse" role="button"
                           aria-expanded="false" aria-controls="UserCollapse">
                            <div class="dashboard_icon">
                                <img src="{{ asset('website') }}/assets/images/User Management.svg">
                            </div>
                            User Management
                            <span><i class="fa-solid fa-chevron-down"></i></span>
                        </a>
                        <ul class="collapse" id="UserCollapse">
                            <li class="menu_content">
                                <a class="menu_link custom_dropdown_menu @if(request()->is("fitters*")) active @endif"
                                   href="{{url('fitters')}}" aria-expanded="false">
                                    Fitters
                                    <span class="sidebar_number">{{ $fitterCount ?? 0 }}</span>
                                </a>
                            </li>
                            <li class="menu_content">
                                <a class="menu_link custom_dropdown_menu @if(request()->is("staff*")) active @endif"
                                   href="{{ route('staffs.index') }}" aria-expanded="false">
                                    Staff
                                    <span class="sidebar_number">{{ $staffCount ?? 0 }}</span>
                                </a>
                            </li>
                            <li class="menu_content">
                                <a class="menu_link custom_dropdown_menu @if(request()->is("customers*")) active @endif"
                                   href="{{ route('customers.index') }}" aria-expanded="false">
                                    Customers
                                    <span class="sidebar_number">{{ $customersCount ?? 0 }}</span>
                                </a>
                            </li>
                        </ul>
                    </li>
                @endif
                @can('product-list')
                    <li class="sidebar_dropdown @if(request()->is("product") || request()->is("create-product") || request()->is("view-product") || request()->is("stock-in") || request()->is("view-stockin") || request()->is("stock-out") || request()->is("view-stockout")) active @endif">
                        <a class="menu_link dropdown_link" href="#ProductCollapse" data-bs-toggle="collapse"
                           role="button" aria-expanded="false" aria-controls="ProductCollapse">
                            <div class="dashboard_icon">
                                <img src="{{ asset('website') }}/assets/images/Product.svg">
                            </div>
                            Product Management
                            <span><i class="fa-solid fa-chevron-down"></i></span>
                        </a>
                        <ul class="collapse" id="ProductCollapse">
                            <li class="menu_content">
                                <a class="menu_link custom_dropdown_menu @if(request()->is("product") || request()->is("create-product") || request()->is("view-product")) active @endif"
                                   href="{{url('product')}}" aria-expanded="false">
                                    Products
                                    <span class="sidebar_number">10</span>
                                </a>
                            </li>
                            <li class="menu_content">
                                <a class="menu_link custom_dropdown_menu @if(request()->is("stock-in") || request()->is("view-stockin")) active @endif"
                                   href="{{url('stock-in')}}" aria-expanded="false">
                                    Stock-In
                                    <span class="sidebar_number">10</span>
                                </a>
                            </li>
                            <li class="menu_content">
                                <a class="menu_link custom_dropdown_menu @if(request()->is("stock-out") || request()->is("view-stockout")) active @endif"
                                   href="{{url('stock-out')}}" aria-expanded="false">
                                    Stock-Out
                                    <span class="sidebar_number">10</span>
                                </a>
                            </li>
                        </ul>
                    </li>
                @endcan
                @if (auth()->user()->can('orders-list'))
                    <li class="sidebar_dropdown @if(request()->is("orders*")) active @endif">
                        <a class="menu_link dropdown_link" data-bs-toggle="collapse" href="#OrderCollapse" role="button"
                           aria-expanded="false" aria-controls="OrderCollapse">
                            <div class="dashboard_icon">
                                <img src="{{ asset('website') }}/assets/images/document_order.svg">
                            </div>
                            Order Management
                            <span><i class="fa-solid fa-chevron-down"></i></span>
                        </a>
                        <ul class="collapse" id="OrderCollapse">
                            <li class="menu_content">
                                <a class="menu_link custom_dropdown_menu @if(request()->is("orders/pending*")) active @endif"
                                   href="{{ route('orders.index', ['status' => 'pending']) }}" aria-expanded="false">
                                    Pending/Quotation
                                    <span class="sidebar_number">{{ $orderCounts['pending'] }}</span>
                                </a>
                            </li>
                            <li class="menu_content">
                                <a class="menu_link custom_dropdown_menu @if(request()->is("orders/paid*") || request()->is("view-purchased")) active @endif"
                                   href="{{ route('orders.index', ['status' => 'paid']) }}" aria-expanded="false">
                                    Purchased
                                    <span class="sidebar_number">{{ $orderCounts['paid'] }}</span>
                                </a>
                            </li>
                            <li class="menu_content">
                                <a class="menu_link custom_dropdown_menu @if(request()->is("orders/completed*")) active @endif"
                                   href="{{ route('orders.index', ['status' => 'completed']) }}" aria-expanded="false">
                                    Completed
                                    <span class="sidebar_number">{{ $orderCounts['completed'] }}</span>
                                </a>
                            </li>
                            <li class="menu_content">
                                <a class="menu_link custom_dropdown_menu @if(request()->is("orders/previous*")) active @endif" href="{{ route('orders.index', ['status' => 'previous']) }}" aria-expanded="false">
                                    Previous
                                    <span class="sidebar_number">{{ $orderCounts['previous'] }}</span>
                                </a>
                            </li>
                        </ul>
                    </li>
                @endif
                @canany(['cuttingstations-list' , 'headingstations-list' , 'loftliestations-list' , 'sstpuringstations-list' , 'epoxystations-list' , 'grippingstations-list' , 'finalcheckstations-list' , 'shippingstations-list'])
                    <li class="sidebar_dropdown @if(request()->is("cuttingstations*") || request()->is("headingstations*") || request()->is("loftliestations*") || request()->is("sstpuringstations*") || request()->is("epoxystations*") || request()->is("grippingstations*") || request()->is("finalcheckstations*") || request()->is("shippingstations*")) active @endif">
                        <a class="menu_link dropdown_link" data-bs-toggle="collapse" href="#ProductionCollapse"
                           role="button" aria-expanded="false" aria-controls="ProductionCollapse">
                            <div class="dashboard_icon">
                                <img src="{{ asset('website') }}/assets/images/document_order.svg">
                            </div>
                            Production Management
                            <span><i class="fa-solid fa-chevron-down"></i></span>
                        </a>
                        <ul class="collapse" id="ProductionCollapse">
                            @can('cuttingstations-list')
                                <li class="menu_content">
                                    <a class="menu_link custom_dropdown_menu @if(request()->is("cuttingstations*")) active @endif"
                                       href="{{ route('cuttingstations.index') }}" aria-expanded="false">
                                        Cutting Stations
                                        <span class="sidebar_number">{{ $orderCounts['cuttingStation'] }}</span>
                                    </a>
                                </li>
                            @endcan
                            @can('headingstations-list')
                                <li class="menu_content">
                                    <a class="menu_link custom_dropdown_menu @if(request()->is("headingstations*")) active @endif"
                                       href="{{ route('headingstations.index') }}" aria-expanded="false">
                                        Head Stations
                                        <span class="sidebar_number">{{ $orderCounts['headStation'] }}</span>
                                    </a>
                                </li>
                            @endcan
                            @can('loftliestations-list')
                                <li class="menu_content">
                                    <a class="menu_link custom_dropdown_menu @if(request()->is("loftliestations*")) active @endif"
                                       href="{{ route('loftliestations.index') }}" aria-expanded="false">
                                        Loft & Lie Stations
                                        <span class="sidebar_number">{{ $orderCounts['loftLieStation'] }}</span>
                                    </a>
                                </li>
                            @endcan
                            @can('sstpuringstations-list')
                                <li class="menu_content">
                                    <a class="menu_link custom_dropdown_menu @if(request()->is("sstpuringstations*")) active @endif"
                                       href="{{ route('sstpuringstations.index') }}" aria-expanded="false">
                                        SST Puring Station
                                        <span class="sidebar_number">{{ $orderCounts['sstPuringStation'] }}</span>
                                    </a>
                                </li>
                            @endcan
                            @can('epoxystations-list')
                                <li class="menu_content">
                                    <a class="menu_link custom_dropdown_menu @if(request()->is("epoxystations*")) active @endif"
                                       href="{{ route('epoxystations.index') }}" aria-expanded="false">
                                        Epoxy Station
                                        <span class="sidebar_number">{{ $orderCounts['epoxyStation'] }}</span>
                                    </a>
                                </li>
                            @endcan
                            @can('grippingstations-list')
                                <li class="menu_content">
                                    <a class="menu_link custom_dropdown_menu @if(request()->is("grippingstations*")) active @endif"
                                       href="{{ route('grippingstations.index') }}" aria-expanded="false">
                                        Gripping Station
                                        <span class="sidebar_number">{{ $orderCounts['grippingStation'] }}</span>
                                    </a>
                                </li>
                            @endcan
                            @can('finalcheckstations-list')
                                <li class="menu_content">
                                    <a class="menu_link custom_dropdown_menu @if(request()->is("finalcheckstations*")) active @endif"
                                       href="{{ route('finalcheckstations.index') }}" aria-expanded="false">
                                        Final Check Station
                                        <span class="sidebar_number">{{ $orderCounts['finalCheckStation'] }}</span>
                                    </a>
                                </li>
                            @endcan
                            @can('shippingstations-list')
                                <li class="menu_content">
                                    <a class="menu_link custom_dropdown_menu @if(request()->is("shippingstations*")) active @endif"
                                       href="{{ route('shippingstations.index') }}" aria-expanded="false">
                                        Shipping Station
                                        <span class="sidebar_number">{{ $orderCounts['shippingStation'] }}</span>
                                    </a>
                                </li>
                            @endcan
                        </ul>
                    </li>
                @endcanany
                @can('integrations-list')
                    <li class="menu_content">
                        <a href="#" class="menu_link">
                            <div class="dashboard_icon">
                                <img src="{{ asset('website') }}/assets/images/integration.svg">
                            </div>
                            Integrations
                        </a>
                    </li>
                @endcan
                @can('notifications-list')
                    <li class="menu_content @if(request()->is("notification")) active @endif">
                        <a href="{{url('notification')}}" class="menu_link">
                            <div class="dashboard_icon">
                                <img src="{{ asset('website') }}/assets/images/notification-bing.svg">
                            </div>
                            Notifications
                        </a>
                    </li>
                @endcan
                @if (auth()->user()->can('category-list') || auth()->user()->can('model-list') || auth()->user()->can('shaft-list') || auth()->user()->can('grip-type-list') || auth()->user()->can('grip-size-list') || auth()->user()->can('hossels-list') || auth()->user()->can('lie-angle-list') || auth()->user()->can('face-angle-list') || auth()->user()->can('loft-list') || auth()->user()->can('length-list'))
                    <li class="sidebar_dropdown @if(request()->is("grip-types*")||request()->is("grip-sizes*")|| request()->is("loft*") || request()->is("shflen*") || request()->is("shafts*")  || request()->is("models") || request()->is("lie-angles*") || request()->is("face-angles*") || request()->is("categories") || request()->is("crud-bill-code") || request()->is("crud-charge-code") || request()->is("shf-flx") || request()->is("shf-type") || request()->is("hossels") || request()->is("head-wgtrng")) active @endif">
                        <a class="menu_link dropdown_link" data-bs-toggle="collapse" href="#SettingsCollapse"
                           role="button" aria-expanded="false" aria-controls="SettingsCollapse">
                            <div class="dashboard_icon">
                                <img src="{{ asset('website') }}/assets/images/setting.svg">
                            </div>
                            Settings
                            <span><i class="fa-solid fa-chevron-down"></i></span>
                        </a>
                        <ul class="collapse cruds_collapse @if(request()->is('shafts') || request()->is('shf-type') || request()->is('shf-flx') || request()->is('head-wgtrng')) show @endif"
                            id="SettingsCollapse">
                            <li class="sidebar_dropdown">
                                <a class="menu_link dropdown_link custom_dropdown_menu cruds_active_link"
                                   data-bs-toggle="collapse" href="#CrudCollapse" role="button" aria-expanded="false"
                                   aria-controls="CrudCollapse">
                                    Cruds<span class="sidebar_number">15</span>
                                </a>
                                <ul class="collapse @if(request()->is('shafts') || request()->is('shf-type') || request()->is('shf-flx') || request()->is('head-wgtrng')) show @endif"
                                    id="CrudCollapse">
                                    <li class="menu_content"><a
                                            class="menu_link custom_dropdown_menu @if(request()->is("categories")) active @endif"
                                            href="{{url('categories')}}">
                                            Categories
                                            <span class="sidebar_number">{{ $categoriesCount ?? 0 }}</span>
                                        </a>
                                    </li>
                                    <li class="menu_content">
                                        <a class="menu_link custom_dropdown_menu @if(request()->is("models")) active @endif"
                                           href="{{url('models')}}">
                                            Model
                                            <span class="sidebar_number">{{ $modelsCount ?? 0 }}</span>
                                        </a>
                                    </li>
                                    <li class="menu_content sidebar_dropdown">
                                        <a class="menu_link custom_dropdown_menu shaft_active_link @if(request()->is('shafts') || request()->is('shf-type') || request()->is('shf-flx') || request()->is('head-wgtrng')) active @endif"
                                           href="{{ url('shafts') }}" data-bs-toggle="collapse"
                                           data-bs-target="#ShaftSubmenu" role="button" aria-expanded="false"
                                           aria-controls="ShaftSubmenu"
                                           onclick="event.preventDefault(); window.location='{{ url('shafts') }}'; toggleShaftMenu();">
                                            Shaft
                                            <span class="sidebar_number">{{ $shaftsCount ?? 0 }}</span>
                                        </a>
                                        <ul class="collapse @if(request()->is('shafts') || request()->is('shf-type') || request()->is('shf-flx') || request()->is('head-wgtrng')) show @endif"
                                            id="ShaftSubmenu">
                                            <li class="menu_content">
                                                <a class="menu_link custom_dropdown_menu @if(request()->is('shf-type')) active @endif"
                                                   href="{{ url('/shf-type') }}">
                                                    Material
                                                    <span class="sidebar_number">{{ $shaftMaterialCount ?? 0 }}</span>
                                                </a>
                                            </li>
                                            <li class="menu_content">
                                                <a class="menu_link custom_dropdown_menu @if(request()->is('shf-flx')) active @endif"
                                                   href="{{ url('/shf-flx') }}">
                                                    Flex
                                                    <span class="sidebar_number">{{ $shaftFlexCount ?? 0 }}</span>
                                                </a>
                                            </li>
                                            <li class="menu_content">
                                                <a class="menu_link custom_dropdown_menu @if(request()->is('head-wgtrng')) active @endif"
                                                   href="{{ url('head-wgtrng') }}">
                                                    Weight
                                                    <span class="sidebar_number">{{ $shaftWeightCount ?? 0 }}</span>
                                                </a>
                                            </li>
                                        </ul>
                                    </li>
                                    <li class="menu_content">
                                        <a class="menu_link custom_dropdown_menu @if(request()->is("grip-types*")) active @endif"
                                           href="{{url('grip-types')}}">
                                            Grip Type
                                            <span class="sidebar_number">{{ $gripTypesCount ?? 0 }}</span>
                                        </a>
                                    </li>
                                    <li class="menu_content">
                                        <a class="menu_link custom_dropdown_menu @if(request()->is("grip-sizes*")) active @endif"
                                           href="{{url('grip-sizes')}}">
                                            Grip Size
                                            <span class="sidebar_number">{{ $gripSizesCount ?? 0 }}</span>
                                        </a>
                                    </li>
                                    <li class="menu_content">
                                        <a class="menu_link custom_dropdown_menu @if(request()->is("lie-angles*")) active @endif"
                                           href="{{url('lie-angles')}}">
                                            Lie Angle
                                            <span class="sidebar_number">{{ $lieAngleCount ?? 0 }}</span>
                                        </a>
                                    </li>
                                    <li class="menu_content">
                                        <a class="menu_link custom_dropdown_menu @if(request()->is("face-angles*")) active @endif"
                                           href="{{url('face-angles')}}">
                                            Face Angle
                                            <span class="sidebar_number">{{ $faceAngleCount ?? 0 }}</span>
                                        </a>
                                    </li>
                                    <li class="menu_content">
                                        <a class="menu_link custom_dropdown_menu @if(request()->is("loft*")) active @endif"
                                           href="{{url('loft')}}">
                                            Loft
                                            <span class="sidebar_number">{{ $loftCount ?? 0 }}</span>
                                        </a>
                                    </li>
                                    <li class="menu_content">
                                        <a class="menu_link custom_dropdown_menu @if(request()->is("hossels*")) active @endif"
                                           href="{{url('hossels')}}">
                                            Hossel
                                            <span class="sidebar_number">{{ $hosselCount ?? 0 }}</span>
                                        </a>
                                    </li>
                                    <li class="menu_content">
                                        <a class="menu_link custom_dropdown_menu @if(request()->is("shflen*")) active @endif"
                                           href="{{url('shflen')}}">
                                            Length
                                            <span class="sidebar_number">{{ $shaftLenCount ?? 0 }}</span>
                                        </a>
                                    </li>
                                    <li class="menu_content">
                                        <a class="menu_link custom_dropdown_menu @if(request()->is("bill-code")) active @endif"
                                           href="{{url('bill-code')}}">
                                            Bill of Code
                                            <span class="sidebar_number">{{ $billCodeCount ?? 0 }}</span>
                                        </a>
                                    </li>
                                    <li class="menu_content">
                                        <a class="menu_link custom_dropdown_menu @if(request()->is("no-charge-code")) active @endif"
                                           href="{{url('no-charge-code')}}">
                                            No charge Code
                                            <span class="sidebar_number">{{ $noChargeCodeCount ?? 0 }}</span>
                                        </a>
                                    </li>
                                    <li class="menu_content">
                                        <a class="menu_link custom_dropdown_menu @if(request()->is("ord-type")) active @endif"
                                           href="{{url('ord-type')}}">
                                            Order Type
                                            <span class="sidebar_number">{{ $orderTypeCount ?? 0 }}</span>
                                        </a>
                                    </li>
                                    <li class="menu_content">
                                        <a class="menu_link custom_dropdown_menu @if(request()->is("ship-via")) active @endif"
                                           href="{{url('ship-via')}}">
                                            Ship Via
                                            <span class="sidebar_number">{{ $shipViaCount ?? 0 }}</span>
                                        </a>
                                    </li>
                                    <li class="menu_content">
                                        <a class="menu_link custom_dropdown_menu @if(request()->is("actv-type")) active @endif"
                                           href="{{url('actv-type')}}">
                                            ActvType
                                            <span class="sidebar_number">{{ $actvTypeCount ?? 0 }}</span>
                                        </a>
                                    </li>
                                </ul>
                            </li>
                            <li class="menu_content cruds_stock">
                                <a class="menu_link custom_dropdown_menu" href="#">
                                    Stock-Out
                                </a>
                            </li>
                            <li class="menu_content cruds_stock">
                                <a class="menu_link custom_dropdown_menu" href="#">
                                    Stock-In
                                </a>
                            </li>
                        </ul>
                    </li>
                @endif
                @can('cmshomes-list')
                    <li class="menu_content @if(request()->is("cms")) active @endif">
                        <a class="menu_link  {{ request()->is('cms*') ? 'active' : '' }}"
                           href="{{url('/cms?tab=common-settings')}}">
                            <div class="dashboard_icon">
                                <img src="{{ asset('website') }}/assets/images/cms.svg">
                            </div>
                            Content Management
                        </a>
                    </li>
                @endcan
                @can('contacts-list')
                    <li class="menu_content @if(request()->is("contacts")) active @endif">
                        <a class="menu_link" href="{{url('contacts')}}">
                            <div class="dashboard_icon">
                                <img src="{{ asset('website') }}/assets/images/Property.svg">
                            </div>
                            Contacts
                        </a>
                    </li>
                @endcan
                @if(auth()->user()->hasRole('developer'))
                    <li class="menu_content">
                        <a class="menu_link" href="{{asset('crud_generator')}}">
                            CRUD Generator
                        </a>
                    </li>
                    <li class="menu_content">
                        <a class="menu_link" href="{{asset('roles')}}">
                            Roles
                        </a>
                    </li>
                    <li class="sidebar_dropdown">
                        <a class="menu_link dropdown_link" data-bs-toggle="collapse" href="#UsersCollapse" role="button"
                           aria-expanded="false" aria-controls="UsersCollapse">
                            <div class="dashboard_icon">
                                <i class="fa-solid fa-screwdriver-wrench"></i>
                            </div>
                            Users
                            <span><i class="fa-solid fa-chevron-down"></i></span>
                        </a>
                        <ul class="collapse" id="UsersCollapse">
                            <li class="menu_content">
                                <a class="menu_link" href="{{asset('users')}}">
                                    Manage Users
                                </a>
                            </li>
                            <li class="menu_content">
                                <a class="menu_link" href="{{asset('user/create')}}">
                                    Add New User
                                </a>
                            </li>
                            <li class="menu_content">
                                <a class="menu_link" href="{{asset('user/deleted')}}">
                                    Deleted Users
                                </a>
                            </li>
                        </ul>
                    </li>
                @endif
                {{--                @foreach ($crud as $item)--}}
                {{--                    @can($item->url . '-list')--}}
                {{--                        <li class="menu_content">--}}
                {{--                            <a class="menu_link" href="{{ url($item->url) }}">--}}
                {{--                                <div class="dashboard_icon"><i class="fa-solid {{$item->icon??"fa-dashboard"}}"></i>--}}
                {{--                                </div>--}}
                {{--                                {{ preg_replace('/(?<=[a-z])[A-Z]|[A-Z](?=[a-z])/', ' $0', $item->title) }}--}}
                {{--                            </a>--}}
                {{--                        </li>--}}
                {{--                    @endcan--}}
                {{--                @endforeach--}}
                <li class="menu_content custom_logout">
                    <a class="menu_link" href="{{url('logout')}}">
                        <div class="dashboard_icon">
                            <img src="{{ asset('website') }}/assets/images/logout.svg">
                        </div>
                        Logout
                    </a>
                </li>
            </ul>
        </nav>
    </div>
</aside>
