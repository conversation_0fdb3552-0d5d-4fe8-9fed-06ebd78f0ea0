<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class AcctMgr extends Model
{
    use HasFactory , softDeletes;

    protected $table = 'acctmgr';
    protected $guarded = [];
    protected $primaryKey = 'AcctMgrId';
    protected $keyType = 'string';

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }
    public function accountManager(){
        return $this->hasOne(Acct::class,'AcctMgrId','AcctMgrId');
    }
    public function getStatusHtmlAttribute()
    {
        return $this->CurrInd == 1 ? '<span style="cursor:pointer;" class="success change-staff-status"  staff_id="' . $this->AcctMgrId . '" staff_status="0" >Active</span>' : '<span class="danger change-staff-status" staff_id="' . $this->AcctMgrId . '" staff_status="1"  style="cursor:pointer;">Inactive</span>';
    }
}
