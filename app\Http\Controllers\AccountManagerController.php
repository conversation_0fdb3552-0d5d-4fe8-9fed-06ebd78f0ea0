<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\AcctMgr;
use Spatie\Permission\Models\Permission;
use Illuminate\Http\Request;
use Yajra\DataTables\Facades\DataTables;


class AccountManagerController extends Controller
{

    public function index(Request $request)
    {
        if ($request->ajax()) {
            $acctMgrQuery = AcctMgr::with(['user.profile'])->orderByDesc('AcctMgrId');

            if ($request->has('search') && $request->search['value'] != '') {
                $searchValue = $request->search['value'];
                $acctMgrQuery->where(function ($query) use ($searchValue) {
                    $query->where('AcctMgrNm', 'like', "%$searchValue%")
                        ->orWhereHas('user', function ($q) use ($searchValue) {
                            $q->where('email', 'like', "%$searchValue%");
                        })
                        ->orWhereHas('user.profile', function ($q) use ($searchValue) {
                            $q->where('phone_number', 'like', "%$searchValue%");
                        });
                });
            }

            if ($request->has('status') && $request->status != '') {
                $acctMgrQuery->where('CurrInd', $request->status == 'active' ? '1' : '0');
            }


            return DataTables::of($acctMgrQuery)
                ->addColumn('checkbox', function ($row) {
                    return '<input type="checkbox" class="category-checkbox">';
                })
                ->addColumn('name', function ($row) {
                    return $row->user->name ?? 'N/A';
                })
                ->addColumn('account', function ($row) {
                    return $row->accountManager->AcctNm ?? 'N/A';
                })
                ->addColumn('status', function ($row) {
                    return $row->statusHtml;
                })
                ->addColumn('action', function ($row) {
                    return '
                    <div class="dropdown">
                        <button class="btn dropdown-toggle" type="button" id="dropdownMenuButton11" data-bs-toggle="dropdown" aria-expanded="false">
                            <img class="ellipsis_img" src="' . asset('website') . '/assets/images/ellipsis.svg">
                        </button>
                        <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton11">
                            <li><a class="dropdown-item" href="' . route('account-managers.show', $row->AcctMgrId) . '">View</a></li>
                            <li><a class="dropdown-item" href="' . route('account-managers.edit', $row->AcctMgrId) . '">Edit</a></li>
                            <li>
                                <form action="' . route('account-managers.destroy', $row->AcctMgrId) . '" method="POST" class="delete-form">
                                    ' . csrf_field() . '
                                    ' . method_field('DELETE') . '
                                     <a class="dropdown-item" href="javascript:void(0)" onclick="showDeleteConfirmation(this)">Delete</a>
                                </form>
                            </li>
                        </ul>
                    </div>
                ';
                })
                ->rawColumns(['status', 'action', 'name', 'account', 'checkbox'])
                ->make(true);
        }
        return view('dashboard.user-management.AccountManagers.index');
    }


    public function create()
    {
        return view('categories.create');
    }

    public function store(Request $request)
    {
        //
    }

    public function show($id)
    {
        return redirect(url('categories'));
    }

    public function edit($id)
    {
    }

    public function update(Request $request, $id)
    {
        //
    }

    public function destroy($id)
    {
       //
    }

    public function updateStatus(Request $request, $id)
    {
        //
    }


}
