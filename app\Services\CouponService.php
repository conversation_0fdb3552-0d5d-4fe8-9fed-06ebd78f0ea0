<?php

namespace App\Services;

use App\Models\BillCd;
use App\Models\NoChargeCode;
use Illuminate\Support\Facades\Session;

class CouponService
{
    /**
     * Apply coupon/bill code to the order
     */
    public function applyCoupon($couponCode)
    {
        if (empty($couponCode)) {
            return [
                'success' => false,
                'message' => 'Please enter a coupon code'
            ];
        }

        // First check if it's a Bill Code
        $billCode = BillCd::where('BillCdDsc', $couponCode)->where('CurrInd', 1)->first();
        $compCode = null;
        $discountType = null;

        if ($billCode) {
            $discountType = 'bill';
            if (!$billCode->isValidForDiscount()) {
                return [
                    'success' => false,
                    'message' => 'This bill code is not active'
                ];
            }
        } else {
            // Check if it's a Comp Code
            $compCode = NoChargeCode::where('CompCdDsc', $couponCode)->where('CurrInd', 1)->first();

            if ($compCode) {
                $discountType = 'comp';
                if (!$compCode->isValidForDiscount()) {
                    return [
                        'success' => false,
                        'message' => 'This comp code is not active'
                    ];
                }
            } else {
                return [
                    'success' => false,
                    'message' => 'Invalid coupon code'
                ];
            }
        }

        // Get current order data from session
        $orderData = session('order_data', [
            'clubs' => [],
            'order' => ['TtlAmt' => 0]
        ]);

        $currentTotal = $orderData['order']['TtlAmt'] ?? 0;

        if ($currentTotal <= 0) {
            return [
                'success' => false,
                'message' => 'No items in cart to apply discount'
            ];
        }

        // Calculate discount based on type
        if ($discountType === 'bill') {
            $discountAmount = $billCode->calculateDiscount($currentTotal);
            $discountPercentage = $billCode->getDiscountPercentage();
            $codeType = 'Bill Code';
        } else {
            $discountAmount = $compCode->calculateDiscount($currentTotal);
            $discountPercentage = $compCode->getDiscountPercentage();
            $codeType = 'Comp Code';
        }

        $newTotal = $currentTotal - $discountAmount;

        // Update session with coupon information
        $orderData['coupon'] = [
            'code' => $couponCode,
            'type' => $codeType,
            'discount_amount' => $discountAmount,
            'discount_percentage' => $discountPercentage,
            'original_total' => $currentTotal,
            'new_total' => $newTotal
        ];

        // Update order total and set appropriate field
        $orderData['order']['TtlAmt'] = $newTotal;

        if ($discountType === 'bill') {
            $orderData['order']['BillCd'] = $couponCode;
            unset($orderData['order']['CompCd']); // Remove comp code if exists
        } else {
            $orderData['order']['CompCd'] = $couponCode;
            unset($orderData['order']['BillCd']); // Remove bill code if exists
        }

        session(['order_data' => $orderData]);

        return [
            'success' => true,
            'message' => $codeType . ' applied successfully! ' . $discountPercentage . '% discount',
            'discount' => $discountPercentage,
            'discount_amount' => $discountAmount,
            'new_total' => $newTotal,
            'original_total' => $currentTotal
        ];
    }

    /**
     * Remove coupon from the order
     */
    public function removeCoupon()
    {
        $orderData = session('order_data', [
            'clubs' => [],
            'order' => []
        ]);

        if (isset($orderData['coupon'])) {
            // Restore original total
            $orderData['order']['TtlAmt'] = $orderData['coupon']['original_total'];
            unset($orderData['coupon']);
            unset($orderData['order']['BillCd']);
            unset($orderData['order']['CompCd']);

            session(['order_data' => $orderData]);
        }

        return [
            'success' => true,
            'message' => 'Coupon removed successfully'
        ];
    }

    /**
     * Get current applied coupon information
     */
    public function getCurrentCoupon()
    {
        $orderData = session('order_data', []);
        return $orderData['coupon'] ?? null;
    }

    /**
     * Check if coupon is already applied
     */
    public function isCouponApplied()
    {
        return $this->getCurrentCoupon() !== null;
    }
}
