@extends('theme.layout.master')

@push('css')
@endpush

@section('content')
    <section class="main-dashboard">
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-12">
                    <div class="white_box table_box">
                        <div class="table_header">
                            <div class="user_engagement">
                                <h5>Account Manager</h5>
                            </div>
                            <div class="side_fields custom_entries">
                                <div class="custom_action_fields">
                                    <div class="custom_search_box">
                                        <form>
                                            <div class="txt_field">
                                                <i class="fa-solid fa-magnifying-glass"></i>
                                                <input type="search" placeholder="Search" class="form-control searchinput">
                                            </div>
                                        </form>
                                    </div>
                                    <!-- Create Button -->
                                    <a href="{{ route('account-managers.create') }}" class="btn dark_green_btn"><i class="fa-solid fa-square-plus"></i>Create</a>
                                </div>

                            </div>
                            <div class="status_buttons">
                                <button id="activateSelected" class="btn btn-success" title="Active">  <i class="fas fa-check-circle"></i> </button>
                                <button id="deactivateSelected" class="btn btn-danger" title="Deactive">     <i class="fas fa-ban"></i> </button>
                            </div>
                        </div>

                        <div class="table-responsive">
                            <table class="account-managers-datatable table table-row-bordered gy-5">
                                <thead>
                                <tr>
                                    <th>
                                        <input type="checkbox" class="select_all_checkboxes">
                                    </th>
                                    <th>SR#</th>
                                    <th>Name</th>
                                    <th>Account</th>
                                    <th>Status</th>
                                    <th>Action</th>
                                </tr>
                                </thead>

                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

@endsection

@push('js')
    <script>
        $(document).ready(function () {
            // Create custom page length dropdown with 10, 25, 50, and All options
            var htmlContent = '<div class="dt-layout-cell dt-layout-start"><div class="dt-length"><label for="dt-length-0">Show</label><select aria-controls="DataTables_Table_0" class="dt-input" id="dt-length-0"><option value="10">10</option><option value="25">25</option><option value="50">50</option><option value="-1">All</option></select><label for="dt-length-0">entries</label></div></div>';

            // Append the dropdown to the side fields container
            document.querySelector('.side_fields.custom_entries').insertAdjacentHTML('afterbegin', htmlContent);

            var table = $('.account-managers-datatable').DataTable({
                processing: true,
                serverSide: true,
                ajax: {
                    url: '{{ route('account-managers.index') }}',
                    type: 'GET',
                    data: function (d) {
                        d.status = $('#status').val();
                    },
                    dataSrc: 'data'
                },
                columns: [
                    { data: 'checkbox', name: 'checkbox' },
                    {data: null, name: null, defaultContent: ''},
                    {data: 'name', name: 'name'},
                    {data: 'account', name: 'account'},
                    {data: 'status', name: 'status'},
                    {data: 'action', name: 'action', orderable: false, searchable: false}
                ],
                order: [[1, 'desc']],
                pageLength: 10,
                lengthChange: false,
                ordering: false,
                searching: true,
                createdRow: function (row, data, dataIndex) {
                    $(row).find('td:eq(1)').html(dataIndex + 1);
                },
                initComplete: function () {
                    $('[data-bs-toggle="tooltip"]').tooltip();
                }
            });

            // Change the page length based on dropdown selection
            document.querySelector('#dt-length-0').addEventListener('change', function() {
                var selectedValue = this.value;
                if (selectedValue == "-1") {
                    table.page.len(-1).draw(); // Show all records
                } else {
                    table.page.len(parseInt(selectedValue)).draw(); // Set page length to selected value
                }
            });
            $(document).on("input", '.searchinput', function () {
                var searchValue = $(this).val();
                table.search(searchValue).draw();
            });
        });

        {{--$(document).on('click', '.change-staff-status', function () {--}}
        {{--    var staffId = $(this).attr('staff_id');--}}
        {{--    var status = $(this).attr('staff_status');--}}
        {{--    var current_parent = $(this).parent();--}}
        {{--    Swal.fire({--}}
        {{--        title: 'Are you sure?',--}}
        {{--        text: 'You are about to change the status.',--}}
        {{--        icon: 'warning',--}}
        {{--        showCancelButton: true,--}}
        {{--        confirmButtonColor: '#3085d6',--}}
        {{--        cancelButtonColor: '#d33',--}}
        {{--        confirmButtonText: 'Yes, change it!'--}}
        {{--    }).then((result) => {--}}
        {{--        if (result.isConfirmed) {--}}
        {{--            var url = "{{ url('staffs') }}/" + staffId + "/status";--}}
        {{--            $.ajax({--}}
        {{--                type: "POST",--}}
        {{--                url: url,--}}
        {{--                data: {--}}
        {{--                    _token: "{{ csrf_token() }}",--}}
        {{--                    status: status--}}
        {{--                },--}}
        {{--                success: function (response) {--}}
        {{--                    if (response.success) {--}}
        {{--                        current_parent.html(response.resultHtml);--}}
        {{--                        swal.fire({--}}
        {{--                            icon: 'success',--}}
        {{--                            title: 'Success',--}}
        {{--                            text: response.message,--}}
        {{--                            timer: 4000,--}}
        {{--                        });--}}
        {{--                    } else {--}}
        {{--                        swal.fire({--}}
        {{--                            icon: 'error',--}}
        {{--                            title: 'Error',--}}
        {{--                            text: response.message,--}}
        {{--                            timer: 4000,--}}

        {{--                        });--}}
        {{--                    }--}}
        {{--                },--}}
        {{--                error: function (xhr, status, error) {--}}
        {{--                    swal.fire({--}}
        {{--                        icon: 'error',--}}
        {{--                        title: 'Error',--}}
        {{--                        text: 'An error occurred while changing the status.',--}}
        {{--                        timer: 4000,--}}
        {{--                    });--}}
        {{--                }--}}
        {{--            });--}}
        {{--        }--}}
        {{--    });--}}
        {{--});--}}

    </script>
@endpush
