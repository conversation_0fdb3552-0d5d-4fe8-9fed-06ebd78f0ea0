<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;

use App\Models\HeadWgtrng;
use DB;
use Illuminate\Http\Request;
use Yajra\DataTables\Facades\DataTables;

class HeadWgtrngController extends Controller
{

    public function index(Request $request)
    {
        if ($request->ajax()) {
            $shaftQuery = HeadWgtrng::latest();
            if ($request->has('search') && $request->search['value'] != '') {
                $searchValue = $request->search['value'];
                $shaftQuery->where(function ($query) use ($searchValue) {
                    $query->where('HeadWgtRngDsc', 'like', "%$searchValue%")
                        ->orWhere('HeadWgtRngCd', 'like', "%$searchValue%");
                });
            }
            if ($request->has('status') && $request->status != '') {
                $shaftQuery->where('status', $request->status == 'active' ? '1' : '0');
            }

            return DataTables::of($shaftQuery)
                ->addColumn('checkbox', function ($row) {
                    return '<input type="checkbox" class="category-checkbox" value="' . $row->HeadWgtRngCd . '">';
                })
                ->addColumn('name', function ($row) {
                    return $row->HeadWgtRngDsc ?? 'N/A';
                })
                ->addColumn('price', function ($row) {
                    return $row->price ?? 'N/A';
                })
                ->addColumn('status', function ($row) {
                    return $row->statusHtml ??'';
                })
                ->addColumn('action', function ($row) {
                    return '
                <div class="dropdown">
                    <button class="btn dropdown-toggle" type="button" id="dropdownMenuButton11" data-bs-toggle="dropdown" aria-expanded="false">
                        <img class="ellipsis_img" src="' . asset('website') . '/assets/images/ellipsis.svg">
                    </button>
                    <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton11">
                         <li><a class="dropdown-item edit_model_button" href="javascript:void(0)"
                                       model_id="' . $row->HeadWgtRngCd . '"
                                       model_Price="' . $row->price . '"
                                       model_HeadWgtRngDsc="' . $row->HeadWgtRngDsc . '">Edit</a></li>
                                <li>
                                <form action="' . route('head-wgtrng.destroy', $row->HeadWgtRngCd) . '" method="POST" class="delete-form">
                                    ' . csrf_field() . method_field('DELETE') . '
                                    <a class="dropdown-item" href="javascript:void(0)" onclick="showDeleteConfirmation(this)">Delete</a>
                                </form>
                    </ul>
                </div>';
                })
                ->rawColumns(['status', 'action','checkbox'])
                ->make(true);
        }
        return view('dashboard.Cruds.Weights.index');
    }

    public function create()
    {
        //
    }

    public function store(Request $request)
    {
        extract($request->all());
        $request->validate([
            'HeadWgtRngDsc' => 'required|string',
        ], [
            'HeadWgtRngDsc.required' => 'Flex name is required.',
        ]);
        try {
            DB::beginTransaction();
            $model = HeadWgtrng::create([
                'HeadWgtRngCd' => time(),
                'HeadWgtRngDsc' => $HeadWgtRngDsc,
                'price' =>  isset($request->price)  ? $request->price : null,
            ]);
            DB::commit();
            if ($model) {
                return redirect(url('head-wgtrng'))->with(['title' => 'Done', 'message' => 'shaft weight created successfully.', 'type' => 'success']);
            } else {
                return redirect(url('head-wgtrng'))->with(['title' => 'Fail', 'message' => 'Unable to create shaft weight.', 'type' => 'error']);
            }
        } catch (\Exception $e) {
            DB::rollback();
            return redirect(url('head-wgtrng'))->with(['title' => 'Fail', 'message' => 'Unable to create shaft, please try again.' . $e->getMessage(), 'type' => 'error']);
        }

    }

    public function show($id)
    {
        //
    }

    public function edit($id)
    {
        //
    }

    public function update(Request $request, $shaftId = null)
    {
        $shaft = HeadWgtrng::where('HeadWgtrngCd', $request->model_id_update)->firstOrFail();

        $request->validate([
            'HeadWgtRngDsc' => 'required|string',
        ], [
            'HeadWgtRngDsc.required' => 'Flex name is required.',
        ]);

        try {
            DB::beginTransaction();
            $shaft->update([
                'HeadWgtRngDsc' => $request->HeadWgtRngDsc,
                'price' =>  isset($request->price)  ? $request->price : null,

            ]);
            DB::commit();
            return redirect()->back()->with([ 'title' => 'Done', 'type' => 'success', 'message' => 'Shaft Weight Updated Successfully']);

        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->back()->with('error', $e->getMessage());
        }
    }

    public function destroy($id)
    {
        try {
            HeadWgtrng::where('HeadWgtrngCd', $id)->update(['deleted_at' => now()]);
            return redirect()->route('head-wgtrng.index')->with(['title' => 'Done', 'message' => 'Shaft Weight deleted successfully.', 'type' => 'success']);
        } catch (\Exception $e) {
            return redirect()->route('head-wgtrng.index')->with(['title' => 'Fail', 'message' => 'Unable to delete Shaft Weight.', 'type' => 'error']);
        }
    }

    public function updateStatus(Request $request, $id)
    {
        if ($request->method() == 'GET') {
            return response()->json([
                'success' => false,
                'message' => 'Invalid request method.',
            ], 400);
        }

        $model = HeadWgtrng::where('HeadWgtRngCd', $id)->firstOrFail();

        $request->validate([
            'status' => 'required|in:0,1',
        ], [
            'status.required' => 'Please select a status.',
            'status.in' => 'Status must be either Active (1) or Inactive (0).',
        ]);

        try {
            DB::beginTransaction();

            // Update status
            $model->status = $request->status;
            $model->save();

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Shaft weight status updated successfully.',
                'status' => $model->status,
                'resultHtml' => ($model->status == 1) ? '<span style="cursor:pointer;" class="success change-model-status"  model_id="' . $model->HeadWgtRngCd . '" model_status="0" >Active</span>' : '<span style="cursor:pointer;" class="danger change-model-status"  model_id="' . $model->HeadWgtRngCd . '" model_status="1" >Inactive</span>'
            ], 200);
        } catch (\Exception $e) {
            DB::rollback();
            return response()->json([
                'success' => false,
                'message' => 'Unable to update Shaft weight status, please try again.',
                'error' => $e->getMessage(),
            ], 500);
        }
    }
}
