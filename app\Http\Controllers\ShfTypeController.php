<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;

use App\Models\ClbType;
use App\Models\ShfGrp;
use App\Models\ShfType;
use App\Models\HeadDsgn;
use DB;
use Illuminate\Http\Request;
use Ya<PERSON>ra\DataTables\Facades\DataTables;

class ShfTypeController extends Controller
{
    public function __construct()
    {
        $categories = ClbType::latest()->get();
        view()->share('categories', $categories);
    }

    public function index(Request $request)
    {
        $shfGrps = ShfGrp::get(['ShfGrpId','ShfGrpDsc']);
        if ($request->ajax()) {
            $shaftQuery = ShfType::latest();
            if ($request->has('search') && $request->search['value'] != '') {
                $searchValue = $request->search['value'];
                $shaftQuery->where(function ($query) use ($searchValue) {
                    $query->where('ShfTypeDsc', 'like', "%$searchValue%")
                        ->orWhere('ShfTypeCd', 'like', "%$searchValue%");
                });
            }
            if ($request->has('status') && $request->status != '') {
                $shaftQuery->where('status', $request->status == 'active' ? '1' : '0');
            }

            return DataTables::of($shaftQuery)
                ->addColumn('checkbox', function ($row) {
                    return '<input type="checkbox" class="category-checkbox" value="' . $row->ShfTypeCd . '">';
                })
                ->addColumn('name', function ($row) {
                    return $row->ShfTypeDsc ?? 'N/A';
                })
                ->addColumn('type', function ($row) {
                    return $row->ShfGrp->ShfGrpDsc ?? 'N/A';
                })
                ->addColumn('price', function ($row) {
                    return $row->price ?? '0';
                })
                ->addColumn('wood', function ($row) {
                    return $row->woodHtml ?? '';
                })
                ->addColumn('iron', function ($row) {
                    return $row->ironHtml ??'';
                })
                ->addColumn('status', function ($row) {
                    return $row->statusHtml ??'';
                })
                ->addColumn('action', function ($row) {
                    return '
                <div class="dropdown">
                    <button class="btn dropdown-toggle" type="button" id="dropdownMenuButton11" data-bs-toggle="dropdown" aria-expanded="false">
                        <img class="ellipsis_img" src="' . asset('website') . '/assets/images/ellipsis.svg">
                    </button>
                    <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton11">
                        <li><a class="dropdown-item edit_model_button" href="javascript:void(0)"
                               model_id="' . $row->ShfTypeCd . '"
                               model_ShfTypeDsc="' . $row->ShfTypeDsc . '"
                               model_Price="' . $row->price . '"
                               model_ShfGrpId="' . $row->ShfGrpId . '"
                               model_IronInd="' . $row->IronInd . '"
                               model_WoodInd="' . $row->WoodInd . '">Edit</a></li>
                        <li>
                                <form action="' . route('shf-type.destroy', $row->ShfTypeCd) . '" method="POST" class="delete-form">
                                    ' . csrf_field() . method_field('DELETE') . '
                                                                   <a class="dropdown-item" href="javascript:void(0)" onclick="showDeleteConfirmation(this)">Delete</a>
                            </form>
                    </ul>
                </div>';
                })
                ->rawColumns(['price','wood','iron','status', 'action','checkbox'])
                ->make(true);
        }
        return view('dashboard.Cruds.Materials.index', compact('shfGrps'));
    }

    public function create()
    {
        //
    }

    public function store(Request $request)
    {
        extract($request->all());
        $request->validate([
            'ShfTypeDsc' => 'required|string',
            'ShfGrpId' => 'required',
        ], [
            'ShfTypeDsc.required' => 'Material name is required.',
            'ShfGrpId.required' => 'Please select Material type.',
        ]);
        try {
            DB::beginTransaction();
            $model = ShfType::create([
                'ShfTypeCd' => time(),
                'ShfTypeDsc' => $ShfTypeDsc,
                'ShfGrpId' => $ShfGrpId,
                'WoodInd' => $WoodInd ??'0',
                'IronInd' => $IronInd ??'0',
                'price' =>  isset($request->price)  ? $request->price : null,
            ]);
            DB::commit();
            if ($model) {
                return redirect(url('shf-type'))->with(['title' => 'Done', 'message' => 'shaft material created successfully.', 'type' => 'success']);
            } else {
                return redirect(url('shf-type'))->with(['title' => 'Fail', 'message' => 'Unable to create shaft material.', 'type' => 'error']);
            }
        } catch (\Exception $e) {
            DB::rollback();
            return redirect(url('shf-type'))->with(['title' => 'Fail', 'message' => 'Unable to create shaft, please try again.' . $e->getMessage(), 'type' => 'error']);
        }

    }

    public function show($id)
    {
        //
    }

    public function edit($id)
    {
        //
    }

    public function update(Request $request, $shaftId = null)
    {
        $shaft = ShfType::where('ShfTypeCd', $request->model_id_update)->firstOrFail();

        $request->validate([
            'ShfTypeDsc' => 'required|string',
            'ShfGrpId' => 'required',
        ], [
            'ShfTypeDsc.required' => 'Material name is required.',
            'ShfGrpId.required' => 'Please select Material Type.',
        ]);

        try {
            DB::beginTransaction();

            $shaft->update([
                'ShfTypeDsc' => $request->ShfTypeDsc,
                'ShfGrpId' => $request->ShfGrpId,
                'WoodInd' => $request->WoodInd ??'0',
                'IronInd' => $request->IronInd ??'0',
                'price' =>  isset($request->price)  ? $request->price : null,
            ]);

            DB::commit();

            return redirect()->back()->with([ 'title' => 'Done', 'type' => 'success', 'message' => 'Material Updated Successfully']);

        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->back()->with('error', $e->getMessage());

        }
    }

    public function destroy($id)
    {
        try {
            ShfType::where('ShfTypeCd', $id)->update(['deleted_at' => now()]);
            return redirect()->route('shf-type.index')->with(['title' => 'Done', 'message' => 'Shaft deleted successfully.', 'type' => 'success']);
        } catch (\Exception $e) {
            return redirect()->route('shf-type.index')->with(['title' => 'Fail', 'message' => 'Unable to delete shaft.', 'type' => 'error']);
        }
    }

    public function updateStatus(Request $request, $id)
    {
        if ($request->method() == 'GET') {
            return response()->json([
                'success' => false,
                'message' => 'Invalid request method.',
            ], 400);
        }

        $model = ShfType::where('ShfTypeCd', $id)->firstOrFail();

        $request->validate([
            'status' => 'required|in:0,1',
        ], [
            'status.required' => 'Please select a status.',
            'status.in' => 'Status must be either Active (1) or Inactive (0).',
        ]);

        try {
            DB::beginTransaction();

            // Update status
            $model->status = $request->status;
            $model->save();

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Shaft Type status updated successfully.',
                'status' => $model->status,
                'resultHtml' => ($model->status == 1) ? '<span style="cursor:pointer;" class="success change-model-status"  model_id="' . $model->ShfTypeCd . '" model_status="0" >Active</span>' : '<span style="cursor:pointer;" class="danger change-model-status"  model_id="' . $model->ShfTypeCd . '" model_status="1" >Inactive</span>'
            ], 200);
        } catch (\Exception $e) {
            DB::rollback();
            return response()->json([
                'success' => false,
                'message' => 'Unable to update Shaft Type status, please try again.',
                'error' => $e->getMessage(),
            ], 500);
        }
    }
}
