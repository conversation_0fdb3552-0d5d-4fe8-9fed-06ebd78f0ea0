<?php

namespace App\Providers;

use App\Models\AcctMgr;
use App\Models\ActvType;
use App\Models\BillCd;
use App\Models\Category;
use App\Models\ClbType;
use App\Models\Cust;
use App\Models\FaceAngle;
use App\Models\HeadDsgn;
use App\Models\HeadWgtrng;
use App\Models\Hossel;
use App\Models\Lie;
use App\Models\Loft;
use App\Models\NoChargeCode;
use App\Models\Ord;
use App\Models\OrdType;
use App\Models\ShfFlx;
use App\Models\ShfLen;
use App\Models\ShfType;
use App\Models\GripType;
use App\Models\Ftr;
use App\Models\GripSz;
use App\Models\ShipVia;
use App\Models\Staff;
use Illuminate\Support\ServiceProvider;
use App\Models\Crud;
use App\Models\putter;

use Illuminate\Support\Facades\View;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        if (\Schema::hasTable('cruds')) {
            $crud = Crud::get();
            view()->share('crud', $crud);
        }

        $categoriesCount = ClbType::where('parent_category_id', '!=', null)->count();
        view()->share('categoriesCount', $categoriesCount);

        $modelsCount = HeadDsgn::count();
        view()->share('modelsCount', $modelsCount);

        $shaftsCount = ShfType::count();
        view()->share('shaftsCount', $shaftsCount);

        $gripTypesCount = GripType::count();
        view()->share('gripTypesCount', $gripTypesCount);

        $gripSizesCount = GripSz::count();
        view()->share('gripSizesCount', $gripSizesCount);

        $fitterCount = Ftr::count();
        view()->share('fitterCount', $fitterCount);

        $customersCount = Cust::count();
        view()->share('customersCount', $customersCount);

        $staffCount = Staff::count();
        view()->share('staffCount', $staffCount);

        $acctMgrCount = AcctMgr::count();
        view()->share('acctMgrCount', $acctMgrCount);

        $lieAngleCount = Lie::count();
        view()->share('lieAngleCount', $lieAngleCount);

        $faceAngleCount = FaceAngle::count();
        view()->share('faceAngleCount', $faceAngleCount);

        $shaftMaterialCount = ShfType::count();
        view()->share('shaftMaterialCount', $shaftMaterialCount);

        $shaftFlexCount = ShfFlx::count();
        view()->share('shaftFlexCount', $shaftFlexCount);

        $shaftWeightCount = HeadWgtrng::count();
        view()->share('shaftWeightCount', $shaftWeightCount);

        $loftCount = Loft::count();
        view()->share('loftCount', $loftCount);

        $shaftLenCount = ShfLen::count();
        view()->share('shaftLenCount', $shaftLenCount);

        $hosselCount = Hossel::count();
        view()->share('hosselCount', $hosselCount);

        $orderCounts = [
            'pending' => Ord::where('status', 'pending')->count(),
            'paid' => Ord::where('status', 'paid')->count(),
            'completed' => Ord::where('status', 'completed')->count(),
            'previous' => Ord::where('status', 'previous')->count(),
            'cuttingStation' => Ord::where('production_status', 'Cutting Station')->count(),
            'headStation' => Ord::where('production_status', 'Heading Station')->count(),
            'loftLieStation' => Ord::where('production_status', 'LoftLie Station')->count(),
            'sstPuringStation' => Ord::where('production_status', 'SSTPuring Station')->count(),
            'epoxyStation' => Ord::where('production_status', 'Epoxy Station')->count(),
            'grippingStation' => Ord::where('production_status', 'Gripping Station')->count(),
            'finalCheckStation' => Ord::where('production_status', 'FinalCheck Station')->count(),
            'shippingStation' => Ord::where('production_status', 'Shipping Station')->count(),
        ];
        view()->share('orderCounts', $orderCounts);

        //not dynamic wright now
        $putterCount = 10;
        view()->share('putterCount', $putterCount);

        $billCodeCount = BillCd::count();
        view()->share('billCodeCount', $billCodeCount);

        //not dynamic wright now
        $noChargeCodeCount = 10;
        view()->share('noChargeCodeCount', $noChargeCodeCount);

        $orderTypeCount =OrdType::count() ;
        view()->share('orderTypeCount', $orderTypeCount);

        $shipViaCount = ShipVia::count();
        view()->share('shipViaCount', $shipViaCount);

        $actvTypeCount = ActvType::count();
        view()->share('actvTypeCount', $actvTypeCount);

        $noChargeCodeCount = NoChargeCode::count();
        view()->share('noChargeCodeCount', $noChargeCodeCount);

    }
}





